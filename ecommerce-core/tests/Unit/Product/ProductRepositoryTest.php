<?php

use App\Models\Product;
use App\Repositories\ProductRepository;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->productRepository = app(ProductRepository::class);
    $this->mediaTable = Media::class;
});

test('model', function () {
    expect($this->productRepository->model())->toBe(Product::class);
});


test('setImage sets product main image', function () {
    $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

    $result = $this->productRepository->setImage($this->product, $imageUrl);

    expect($result)->toBeInstanceOf(ProductRepository::class);

    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Product::class,
        'model_id' => $this->product->id,
        'collection_name' => 'image',
    ]);

    // Verify only one image in the collection (singleFile)
    expect($this->product->fresh()->getMedia('image'))->toHaveCount(1);
});

test('setImage with null url returns repository without action', function () {
    $result = $this->productRepository->setImage($this->product, null);

    expect($result)->toBeInstanceOf(ProductRepository::class);

    $this->assertDatabaseMissing($this->mediaTable, [
        'model_type' => Product::class,
        'model_id' => $this->product->id,
        'collection_name' => 'image',
    ]);
});

test('setGallery sets multiple product gallery images', function () {
    $galleryUrls = [
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
    ];

    $result = $this->productRepository->setGallery($this->product, $galleryUrls);

    expect($result)->toBeInstanceOf(ProductRepository::class);

    // Verify gallery images were created
    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Product::class,
        'model_id' => $this->product->id,
        'collection_name' => 'gallery',
    ]);

    // Verify correct count of gallery images
    expect($this->product->fresh()->getMedia('gallery'))->toHaveCount(3);
});

test('setGallery with empty array returns repository without action', function () {
    $result = $this->productRepository->setGallery($this->product, []);

    expect($result)->toBeInstanceOf(ProductRepository::class);

    $this->assertDatabaseMissing($this->mediaTable, [
        'model_type' => Product::class,
        'model_id' => $this->product->id,
        'collection_name' => 'gallery',
    ]);
});

test('setGallery clears existing gallery images before adding new ones', function () {
    // Add initial gallery images
    $initialUrls = [
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
    ];

    $this->productRepository->setGallery($this->product, $initialUrls);
    expect($this->product->fresh()->getMedia('gallery'))->toHaveCount(2);

    // Add new gallery images (should replace old ones)
    $newUrls = [
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
    ];

    $this->productRepository->setGallery($this->product, $newUrls);

    // Should have only the new images (3), not old + new (5)
    expect($this->product->fresh()->getMedia('gallery'))->toHaveCount(3);
});

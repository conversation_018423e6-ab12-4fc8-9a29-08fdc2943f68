<?php

use App\Channels\SmsChannel;
use App\Channels\WhatsAppChannel;
use App\Enums\NotificationChannel;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Notifications\GenericNotification;
use App\Services\NotificationService;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Notification;

beforeEach(function () {
    setupRoles();

    // Create basic notification template
    NotificationTemplate::factory()->create([
        'name' => 'test_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Test Subject',
        'content' => 'Test content for {{user_name}}',
        'is_active' => true,
    ]);

    // Create welcome notification template for UserService
    NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome, {{user_name}}!',
        'content' => 'Hello {{user_name}}, welcome! Phone: {{phone_number}}, Email: {{email}}',
        'is_active' => true,
    ]);

    // Create test user
    $this->user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
    ]);
});

describe('NotificationService', function () {

    test('can send notification using Laravel notification system', function () {
        Notification::fake();

        $service = app(NotificationService::class);
        $service->setTemplateName('test_notification')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        Notification::assertSentTo(
            $this->user,
            GenericNotification::class
        );
    });

    test('returns false when no user is set', function () {
        $service = app(NotificationService::class);
        $result = $service->setTemplateName('test_notification')
            ->send(['user_name' => 'John Doe']);

        expect($result)->toBeFalse();
    });

    test('returns false when template not found', function () {
        $service = app(NotificationService::class);
        $result = $service->setTemplateName('nonexistent_template')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        expect($result)->toBeFalse();
    });

    test('can set preferred channels', function () {
        $service = app(NotificationService::class);
        $result = $service->setPreferredChannels([NotificationChannel::SMS]);

        expect($result)->toBeInstanceOf(NotificationService::class);
    });

    test('falls back to WHATSAPP when MAIL template is disabled', function () {
        // Create WHATSAPP template (active)
        NotificationTemplate::factory()->create([
            'name' => 'fallback_test',
            'channel' => NotificationChannel::WHATSAPP,
            'locale' => 'en',
            'subject' => null,
            'content' => 'WhatsApp message for {{user_name}}',
            'is_active' => true,
        ]);

        // Create MAIL template but disabled
        NotificationTemplate::factory()->create([
            'name' => 'fallback_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Email Subject',
            'content' => 'Email content for {{user_name}}',
            'is_active' => false, // Disabled
        ]);

        Notification::fake();

        $service = app(NotificationService::class);
        $service->setTemplateName('fallback_test')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        // Should send notification via WHATSAPP channel
        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $channels = $notification->via($this->user);
                return in_array(WhatsAppChannel::class, $channels);
            }
        );
    });

    test('falls back to SMS when MAIL and WHATSAPP templates are disabled', function () {
        // Create SMS template (active)
        NotificationTemplate::factory()->create([
            'name' => 'sms_fallback_test',
            'channel' => NotificationChannel::SMS,
            'locale' => 'en',
            'subject' => null,
            'content' => 'SMS message for {{user_name}}',
            'is_active' => true,
        ]);

        // Create MAIL template but disabled
        NotificationTemplate::factory()->create([
            'name' => 'sms_fallback_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Email Subject',
            'content' => 'Email content for {{user_name}}',
            'is_active' => false, // Disabled
        ]);

        // Create WHATSAPP template but disabled
        NotificationTemplate::factory()->create([
            'name' => 'sms_fallback_test',
            'channel' => NotificationChannel::WHATSAPP,
            'locale' => 'en',
            'subject' => null,
            'content' => 'WhatsApp message for {{user_name}}',
            'is_active' => false, // Disabled
        ]);

        Notification::fake();

        $service = app(NotificationService::class);
        $service->setTemplateName('sms_fallback_test')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        // Should send notification via SMS channel
        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $channels = $notification->via($this->user);
                return in_array(SmsChannel::class, $channels);
            }
        );
    });

    test('returns false when all preferred channels are disabled', function () {
        // Create all templates but disabled
        $channels = [NotificationChannel::MAIL, NotificationChannel::WHATSAPP, NotificationChannel::SMS];

        foreach ($channels as $channel) {
            NotificationTemplate::factory()->create([
                'name' => 'all_disabled_test',
                'channel' => $channel,
                'locale' => 'en',
                'subject' => $channel === NotificationChannel::MAIL ? 'Subject' : null,
                'content' => "Content for {$channel}",
                'is_active' => false, // All disabled
            ]);
        }

        $service = app(NotificationService::class);
        $result = $service->setTemplateName('all_disabled_test')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        expect($result)->toBeFalse();
    });

    test('prepareTemplateContent replaces template variables correctly', function () {
        // Create template with multiple variables
        NotificationTemplate::factory()->create([
            'name' => 'template_replacement_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Hello {{user_name}}, Order {{order_id}}',
            'content' => 'Dear {{user_name}}, your order {{order_id}} for {{total_amount}} is ready. Contact: {{phone_number}}',
            'is_active' => true,
        ]);

        Notification::fake();

        $templateData = [
            'user_name' => 'John Doe',
            'order_id' => 'ORD-12345',
            'total_amount' => '$99.99',
            'phone_number' => '+1234567890',
        ];

        $service = app(NotificationService::class);
        $service->setTemplateName('template_replacement_test')
            ->setUser($this->user)
            ->setTemplateData($templateData)
            ->send();

        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $notificationArray = $notification->toArray($this->user);
                $content = $notificationArray['content'];

                // Verify all template variables were replaced in subject
                expect($content['subject'])->toBe('Hello John Doe, Order ORD-12345');

                // Verify all template variables were replaced in content
                expect($content['content'])->toBe('Dear John Doe, your order ORD-12345 for $99.99 is ready. Contact: +1234567890');

                return true;
            }
        );
    });

    test('determineActiveChannel selects first available channel from preferred channels', function () {
        // Create templates for different channels
        NotificationTemplate::factory()->create([
            'name' => 'channel_priority_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Email Subject',
            'content' => 'Email content',
            'is_active' => true,
        ]);

        NotificationTemplate::factory()->create([
            'name' => 'channel_priority_test',
            'channel' => NotificationChannel::SMS,
            'locale' => 'en',
            'subject' => null,
            'content' => 'SMS content',
            'is_active' => true,
        ]);

        NotificationTemplate::factory()->create([
            'name' => 'channel_priority_test',
            'channel' => NotificationChannel::WHATSAPP,
            'locale' => 'en',
            'subject' => null,
            'content' => 'WhatsApp content',
            'is_active' => true,
        ]);

        Notification::fake();

        // Test default preferred channels order (MAIL, WHATSAPP, SMS)
        $service = app(NotificationService::class);
        $service->setTemplateName('channel_priority_test')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        // Should use MAIL channel (first in default preferred channels)
        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $channels = $notification->via($this->user);
                return in_array('mail', $channels);
            }
        );
    });

    test('determineActiveChannel respects custom preferred channels order', function () {
        // Create templates for different channels
        NotificationTemplate::factory()->create([
            'name' => 'custom_priority_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Email Subject',
            'content' => 'Email content',
            'is_active' => true,
        ]);

        NotificationTemplate::factory()->create([
            'name' => 'custom_priority_test',
            'channel' => NotificationChannel::SMS,
            'locale' => 'en',
            'subject' => null,
            'content' => 'SMS content',
            'is_active' => true,
        ]);

        Notification::fake();

        // Set custom preferred channels with SMS first
        $service = app(NotificationService::class);
        $service->setTemplateName('custom_priority_test')
            ->setUser($this->user)
            ->setPreferredChannels([NotificationChannel::SMS, NotificationChannel::MAIL])
            ->send(['user_name' => 'John Doe']);

        // Should use SMS channel (first in custom preferred channels)
        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $channels = $notification->via($this->user);
                return in_array(SmsChannel::class, $channels);
            }
        );
    });

    test('determineActiveChannel skips inactive templates', function () {
        // Create MAIL template but inactive
        NotificationTemplate::factory()->create([
            'name' => 'inactive_skip_test',
            'channel' => NotificationChannel::MAIL,
            'locale' => 'en',
            'subject' => 'Email Subject',
            'content' => 'Email content',
            'is_active' => false, // Inactive
        ]);

        // Create SMS template and active
        NotificationTemplate::factory()->create([
            'name' => 'inactive_skip_test',
            'channel' => NotificationChannel::SMS,
            'locale' => 'en',
            'subject' => null,
            'content' => 'SMS content',
            'is_active' => true,
        ]);

        Notification::fake();

        // Default preferred channels: MAIL, WHATSAPP, SMS
        // Should skip MAIL (inactive) and use SMS
        $service = app(NotificationService::class);
        $service->setTemplateName('inactive_skip_test')
            ->setUser($this->user)
            ->send(['user_name' => 'John Doe']);

        Notification::assertSentTo(
            $this->user,
            GenericNotification::class,
            function ($notification) {
                $channels = $notification->via($this->user);
                return in_array(SmsChannel::class, $channels);
            }
        );
    });
});

describe('GenericNotification', function () {

    test('can create notification with basic data', function () {
        $notification = new GenericNotification(
            'test_notification',
            ['user_name' => 'John'],
            [NotificationChannel::MAIL],
            ['subject' => 'Test', 'content' => 'Hello John']
        );

        expect($notification)->toBeInstanceOf(GenericNotification::class);
    });

    test('via method returns correct channels', function () {
        $notification = new GenericNotification(
            'test_notification',
            [],
            [NotificationChannel::MAIL, NotificationChannel::SMS],
            []
        );

        $channels = $notification->via($this->user);

        expect($channels)->toContain('mail')
            ->and($channels)->toContain(SmsChannel::class);
    });

    test('toMail returns MailMessage', function () {
        $notification = new GenericNotification(
            'test_notification',
            [],
            [NotificationChannel::MAIL],
            ['subject' => 'Test', 'content' => 'Hello']
        );

        $mailMessage = $notification->toMail($this->user);

        expect($mailMessage)->toBeInstanceOf(MailMessage::class);
    });

    test('toSms returns string', function () {
        $notification = new GenericNotification(
            'test_notification',
            [],
            [NotificationChannel::SMS],
            ['content' => 'SMS message']
        );

        $smsContent = $notification->toSms($this->user);

        expect($smsContent)->toBe('SMS message');
    });
});


describe('Custom Channels', function () {

    test('User model has routeNotificationForPhone method', function () {
        expect($this->user->routeNotificationForPhone(null))->toBe($this->user->phone);
    });

    test('SmsChannel can be instantiated', function () {
        $channel = new SmsChannel();
        expect($channel)->toBeInstanceOf(SmsChannel::class);
    });

    test('WhatsAppChannel can be instantiated', function () {
        $channel = new WhatsAppChannel();
        expect($channel)->toBeInstanceOf(WhatsAppChannel::class);
    });
});

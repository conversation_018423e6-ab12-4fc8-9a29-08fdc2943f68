<?php

use App\Models\Product;
use App\Models\ProductVariant;
use App\Services\ProductService;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->productService = app(ProductService::class);
    $this->productTable = Product::class;
    $this->variantTable = ProductVariant::class;
    $this->mediaTable = Media::class;
});

describe('ProductService syncVariants', function () {

    test('syncVariants creates new variants', function () {
        // Create a product first
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        $variants = [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sku' => 'PROD-SMALL'
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'PROD-LARGE'
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were created in database
        $this->assertDatabaseCount($this->variantTable, 2);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Small',
            'price' => '50.00',
            'sku' => 'PROD-SMALL'
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Large',
            'price' => '80.00',
            'sku' => 'PROD-LARGE'
        ]);
    });

    test('syncVariants updates existing variants', function () {
        $product = Product::factory()->create();

        // Create existing variants
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Small',
            'price' => '40.00'
        ]);
        $variant2 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Large',
            'price' => '70.00'
        ]);

        $variants = [
            [
                'id' => $variant1->id,
                'title' => 'Updated Small',
                'price' => '55.00',
                'sku' => 'PROD-SMALL-UPDATED'
            ],
            [
                'id' => $variant2->id,
                'title' => 'Updated Large',
                'price' => '85.00',
                'sku' => 'PROD-LARGE-UPDATED'
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant1->id,
            'title' => 'Updated Small',
            'price' => '55.00',
            'sku' => 'PROD-SMALL-UPDATED'
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant2->id,
            'title' => 'Updated Large',
            'price' => '85.00',
            'sku' => 'PROD-LARGE-UPDATED'
        ]);
    });

    test('syncVariants handles mixed new and existing variants', function () {
        $product = Product::factory()->create();

        // Create one existing variant
        $existingVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Existing Medium',
            'price' => '60.00'
        ]);

        $variants = [
            [
                'id' => $existingVariant->id,
                'title' => 'Updated Medium',
                'price' => '65.00'
            ],
            [
                'title' => 'New Extra Large',
                'price' => '100.00',
                'sku' => 'PROD-XL'
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify existing variant was updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $existingVariant->id,
            'title' => 'Updated Medium',
            'price' => '65.00'
        ]);

        // Verify new variant was created
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'New Extra Large',
            'price' => '100.00',
            'sku' => 'PROD-XL'
        ]);

        $this->assertDatabaseCount($this->variantTable, 2);
    });

    test('syncVariants with empty array returns service', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->syncVariants([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        // No variants should be created
        $this->assertDatabaseCount($this->variantTable, 0);
    });

    test('syncVariants preserves product_id for new variants', function () {
        $product = Product::factory()->create();

        $variants = [
            [
                'title' => 'Test Variant',
                'price' => '25.00',
                'sku' => 'TEST-VAR'
            ]
        ];

        $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Test Variant',
            'price' => '25.00',
            'sku' => 'TEST-VAR'
        ]);
    });
});



describe('ProductService getModel', function () {

    test('getModel loads relationships', function () {
        $product = Product::factory()->create();

        // Create some variants to test relationship loading
        ProductVariant::factory()->count(2)->create([
            'product_id' => $product->id
        ]);

        $result = $this->productService
            ->setModel($product)
            ->getModel();

        expect($result)->toBeInstanceOf(Product::class);
        expect($result->id)->toBe($product->id);

        // Verify relationships are loaded (they should be accessible without additional queries)
        expect($result->relationLoaded('variants'))->toBeTrue();
        expect($result->relationLoaded('type'))->toBeTrue();
    });
});

describe('ProductService integration', function () {

    test('complete product workflow with variants', function () {
        // Create a product
        $product = Product::factory()->create([
            'name' => 'Integration Test Product',
            'price' => '100.00'
        ]);

        // Add variants
        $variants = [
            [
                'title' => 'Small',
                'price' => '80.00',
                'sku' => 'INT-SMALL'
            ],
            [
                'title' => 'Large',
                'price' => '120.00',
                'sku' => 'INT-LARGE'
            ]
        ];

        $service = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($service)->toBeInstanceOf(ProductService::class);

        // Verify variants were created
        $this->assertDatabaseCount($this->variantTable, 2);

        // Test getting the model with relationships
        $productWithRelations = $service->getModel();
        expect($productWithRelations->variants)->toHaveCount(2);
    });

    test('updating existing variants workflow', function () {
        $product = Product::factory()->create();

        // Create initial variants
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Version 1',
            'price' => '50.00'
        ]);

        // Update the variant
        $updatedVariants = [
            [
                'id' => $variant1->id,
                'title' => 'Version 2',
                'price' => '60.00'
            ]
        ];

        $this->productService
            ->setModel($product)
            ->syncVariants($updatedVariants);

        // Verify the variant was updated, not duplicated
        $this->assertDatabaseCount($this->variantTable, 1);
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant1->id,
            'title' => 'Version 2',
            'price' => '60.00'
        ]);
    });
});

describe('ProductService Media Handling', function () {

    test('setImage sets product main image', function () {
        $product = Product::factory()->create();
        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        $result = $this->productService
            ->setModel($product)
            ->setImage($imageUrl);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'image',
        ]);

        // Verify only one image in the collection (singleFile)
        expect($product->fresh()->getMedia('image'))->toHaveCount(1);
    });

    test('setImage with null url returns service without action', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->setImage(null);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'image',
        ]);
    });

    test('setGallery sets multiple product gallery images', function () {
        $product = Product::factory()->create();
        $galleryUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->productService
            ->setModel($product)
            ->setGallery($galleryUrls);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify gallery images were created
        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'gallery',
        ]);

        // Verify correct count of gallery images
        expect($product->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test('setGallery with empty array returns service without action', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->setGallery([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'gallery',
        ]);
    });

    test('setGallery clears existing gallery images before adding new ones', function () {
        $product = Product::factory()->create();

        // Add initial gallery images
        $initialUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->productService
            ->setModel($product)
            ->setGallery($initialUrls);

        expect($product->fresh()->getMedia('gallery'))->toHaveCount(2);

        // Add new gallery images (should replace old ones)
        $newUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->productService
            ->setModel($product)
            ->setGallery($newUrls);

        // Should have only the new images (3), not old + new (5)
        expect($product->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test('media handling integrates with product creation', function () {
        $productData = [
            'name' => 'Test Product with Media',
            'price' => 100.00,
            'status' => 'publish',
        ];

        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';
        $galleryUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->productService
            ->store($productData)
            ->setImage($imageUrl)
            ->setGallery($galleryUrls)
            ->getModel();

        expect($result)->toBeInstanceOf(Product::class);
        expect($result->name)->toBe('Test Product with Media');

        // Verify media was attached correctly
        expect($result->getMedia('image'))->toHaveCount(1);
        expect($result->getMedia('gallery'))->toHaveCount(2);
    });
});

describe('ProductService Variant Gallery Handling', function () {

    test('syncVariants creates variants with gallery', function () {
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        $variants = [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sku' => 'PROD-SMALL',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ]
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'PROD-LARGE',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ]
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were created in database
        $this->assertDatabaseCount($this->variantTable, 2);

        // Get the created variants
        $createdVariants = $product->fresh()->variants;
        expect($createdVariants)->toHaveCount(2);

        // Verify gallery media for each variant
        $smallVariant = $createdVariants->where('title', 'Small')->first();
        $largeVariant = $createdVariants->where('title', 'Large')->first();

        expect($smallVariant->getMedia('gallery'))->toHaveCount(2);
        expect($largeVariant->getMedia('gallery'))->toHaveCount(1);
    });

    test('syncVariants updates existing variants with gallery', function () {
        $product = Product::factory()->create();
        $existingVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Title',
            'price' => '50.00'
        ]);

        $variants = [
            [
                'id' => $existingVariant->id,
                'title' => 'Updated Title',
                'price' => '60.00',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ]
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variant was updated
        $updatedVariant = $existingVariant->fresh();
        expect($updatedVariant->title)->toBe('Updated Title');
        expect($updatedVariant->price)->toBe('60.00');

        // Verify gallery media was set
        expect($updatedVariant->getMedia('gallery'))->toHaveCount(2);
    });

    test('syncVariants handles variants without gallery', function () {
        $product = Product::factory()->create();

        $variants = [
            [
                'title' => 'No Gallery Variant',
                'price' => '50.00',
                'sku' => 'NO-GALLERY'
            ]
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variant was created
        $this->assertDatabaseCount($this->variantTable, 1);

        $createdVariant = $product->fresh()->variants->first();
        expect($createdVariant->title)->toBe('No Gallery Variant');

        // Verify no gallery media was attached
        expect($createdVariant->getMedia('gallery'))->toHaveCount(0);
    });
});

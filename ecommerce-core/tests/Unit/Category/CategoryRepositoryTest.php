<?php

use App\Models\Category;
use App\Repositories\CategoryRepository;

beforeEach(function () {
    $this->categoryRepository = app(CategoryRepository::class);
});

test('setImage adds category image', function () {
    $category = Category::factory()->create();
    $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

    $result = $this->categoryRepository->setImage($category, $imageUrl);
    expect($result)->toBeInstanceOf(CategoryRepository::class);
    $this->assertDatabaseHas('media', [
        'model_type' => Category::class,
        'model_id' => $category->id,
        'collection_name' => 'image',
    ]);
});

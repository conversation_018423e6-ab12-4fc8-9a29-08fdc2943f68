<?php

use App\Enums\ProductType;
use App\Models\Category;
use App\Repositories\CategoryRepository;
use App\Services\CategoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->categoryRepository = app(CategoryRepository::class);
    $this->categoryService = app(CategoryService::class);
    $this->table = 'categories';
});

describe('CategoryService Basic Operations', function () {

    test('can create category with auto-generated slug', function () {
        $data = [
            'name' => 'Electronics & Gadgets',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
            'meta' => ['description' => 'Electronic devices'],
            'order_column' => 1,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category)->toBeInstanceOf(Category::class)
            ->and($category->name)->toBe('Electronics & Gadgets')
            ->and($category->slug)->toBe('electronics-gadgets')
            ->and($category->type)->toBe(ProductType::DEFAULT)
            ->and($category->is_active)->toBeTrue()
            ->and($category->meta)->toBe(['description' => 'Electronic devices'])
            ->and($category->order_column)->toBe(1);

        $this->assertDatabaseHas($this->table, [
            'name' => 'Electronics & Gadgets',
            'slug' => 'electronics-gadgets',
            'type' => ProductType::DEFAULT,
        ]);
    });

    test('can create category with parent', function () {
        // Create parent category
        $parent = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->name)->toBe('Smartphones')
            ->and($category->parent_id)->toBe($parent->id)
            ->and($category->slug)->toBe('smartphones');

        $this->assertDatabaseHas($this->table, [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'slug' => 'smartphones',
        ]);
    });

    test('generates unique slug when duplicate exists', function () {
        // Create first category
        Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->not()->toBe('electronics')
            ->and($category->slug)->toStartWith('electronics-');

        $this->assertDatabaseHas($this->table, [
            'name' => 'Electronics',
            'slug' => $category->slug,
        ]);
    });

    test('can update category and regenerate slug', function () {
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $updateData = [
            'name' => 'Electronics & Technology',
            'is_active' => false,
            'meta' => ['updated' => true],
        ];

        $updatedCategory = $this->categoryService
            ->setModel($category)
            ->update($updateData)
            ->getModel();

        expect($updatedCategory->name)->toBe('Electronics & Technology')
            ->and($updatedCategory->slug)->toBe('electronics-technology')
            ->and($updatedCategory->is_active)->toBeFalse()
            ->and($updatedCategory->meta)->toBe(['updated' => true]);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'name' => 'Electronics & Technology',
            'slug' => 'electronics-technology',
            'is_active' => false,
        ]);
    });

    test('can delete category', function () {
        $category = Category::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $category->id]);

        $this->categoryService->setModel($category)->delete();

        $this->assertSoftDeleted($this->table, ['id' => $category->id]);
    });


    test('set image', function () {
        $category = Category::factory()->create();
        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        $result = $this->categoryService
            ->setModel($category)
            ->setImage($imageUrl);

        expect($result)->toBeInstanceOf(CategoryService::class);

        $this->assertDatabaseHas('media', [
            'model_type' => Category::class,
            'model_id' => $category->id,
            'collection_name' => 'image',
        ]);
    });
});

describe('CategoryService Slug Generation', function () {

    test('generates slug from name with special characters', function () {
        $data = [
            'name' => 'Fashion & Clothing - Women\'s Wear!',
            'type' => ProductType::DEFAULT,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->toBe('fashion-clothing-women-s-wear');
    });

    test('handles multiple duplicate slugs correctly', function () {
        // Create multiple categories with same name
        Category::factory()->create(['name' => 'Books', 'slug' => 'books']);
        Category::factory()->create(['name' => 'Books', 'slug' => 'books-1']);

        $data = [
            'name' => 'Books',
            'type' => ProductType::DEFAULT,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->toBe('books-3');
    });

    test('update does not change slug if name unchanged', function () {
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $updateData = [
            'is_active' => false,
            'meta' => ['updated' => true],
        ];

        $updatedCategory = $this->categoryService
            ->setModel($category)
            ->update($updateData)
            ->getModel();

        expect($updatedCategory->slug)->toBe('electronics'); // Unchanged
    });
});

describe('CategoryService Error Handling', function () {

    test('throws exception when updating non-existent category', function () {
        $this->categoryService->setModel(new Category(['id' => 999]));

        expect(fn() => $this->categoryService->update(['name' => 'Test']))
            ->toThrow(Exception::class);
    });

    test('throws exception when deleting non-existent category', function () {
        $this->categoryService->setModel(new Category(['id' => 999]));

        expect(fn() => $this->categoryService->delete())
            ->toThrow(Exception::class);
    });
});

describe('CategoryService Integration', function () {

    test('can create hierarchical category structure', function () {
        // Create root category
        $electronics = $this->categoryService->store([
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
        ])->getModel();

        // Create child category
        $smartphones = $this->categoryService->store([
            'name' => 'Smartphones',
            'parent_id' => $electronics->id,
            'type' => ProductType::DEFAULT,
        ])->getModel();

        // Create grandchild category
        $iphones = $this->categoryService->store([
            'name' => 'iPhones',
            'parent_id' => $smartphones->id,
            'type' => ProductType::DEFAULT,
        ])->getModel();

        expect($electronics->parent_id)->toBeNull()
            ->and($smartphones->parent_id)->toBe($electronics->id)
            ->and($iphones->parent_id)->toBe($smartphones->id);

        $this->assertDatabaseCount($this->table, 3);
    });

    test('service methods return correct instance for chaining', function () {
        $category = Category::factory()->create();

        $result = $this->categoryService
            ->setModel($category)
            ->update(['name' => 'Updated Name']);

        expect($result)->toBeInstanceOf(CategoryService::class)
            ->and($result->getModel())->toBeInstanceOf(Category::class)
            ->and($result->getModel()->name)->toBe('Updated Name');
    });
});

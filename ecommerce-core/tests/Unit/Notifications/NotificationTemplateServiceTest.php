<?php

use App\Models\NotificationTemplate;
use App\Services\NotificationTemplateService;
use App\Enums\NotificationChannel;
use Illuminate\Support\Arr;

beforeEach(function () {
    $this->notificationTemplateService = app(NotificationTemplateService::class);
    $this->table = NotificationTemplate::class;
});

test('store notification template', function () {
    $data = [
        'name' => 'welcome_email',
        'description' => 'Welcome email template',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome to our platform!',
        'content' => 'Hello {{user_name}}, welcome to our platform!',
        'replacements' => ['user_name', 'email'],
        'is_active' => true,
    ];

    $this->assertDatabaseCount($this->table, 0);

    $response = $this->notificationTemplateService->store($data);

    expect($response->getModel())->toMatchArray([
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => $data['subject'],
        'content' => $data['content'],
        'replacements' => $data['replacements'],
        'is_active' => $data['is_active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => $data['subject'],
        'content' => $data['content'],
        'is_active' => $data['is_active'],
    ]);
});

test('store notification template with minimal data', function () {
    $data = [
        'name' => 'sms_notification',
        'channel' => NotificationChannel::SMS,
        'locale' => 'en',
        'content' => 'Your verification code is {{code}}',
    ];

    $this->assertDatabaseCount($this->table, 0);

    $response = $this->notificationTemplateService->store($data);

    expect($response->getModel())->toMatchArray($data);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, $data);
});

test('update notification template', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'old_template',
        'description' => 'Old description',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Old Subject',
        'content' => 'Old content',
        'is_active' => false,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'updated_template',
        'description' => 'Updated description',
        'channel' => NotificationChannel::SMS,
        'locale' => 'es',
        'subject' => 'Updated Subject',
        'content' => 'Updated content with {{variable}}',
        'replacements' => ['variable'],
        'is_active' => true,
    ];

    $response = $this->notificationTemplateService
        ->setModel($template)
        ->update($data);
    expect($response->getModel())->toMatchArray($data);

    $this->assertDatabaseCount($this->table, 1);
    // For database assertions, exclude replacements array to avoid PostgreSQL JSON comparison issues
    $this->assertDatabaseHas($this->table, array_merge(['id' => $template->id],  Arr::except($data, ['replacements'])));
});

test('update notification template partial data', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'template_to_update',
        'description' => 'Original description',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Original Subject',
        'content' => 'Original content',
        'is_active' => true,
    ]);

    $data = [
        'subject' => 'Updated Subject Only',
        'content' => 'Updated content only',
    ];

    $response = $this->notificationTemplateService
        ->setModel($template)
        ->update($data);

    expect($response->getModel()->name)->toBe('template_to_update') // Should remain unchanged
        ->and($response->getModel()->description)->toBe('Original description') // Should remain unchanged
        ->and($response->getModel()->subject)->toBe('Updated Subject Only')
        ->and($response->getModel()->content)->toBe('Updated content only')
        ->and($response->getModel()->is_active)->toBeTrue(); // Should remain unchanged

    $this->assertDatabaseHas($this->table, [
        'id' => $template->id,
        'name' => 'template_to_update',
        'subject' => 'Updated Subject Only',
        'content' => 'Updated content only',
    ]);
});

test('delete notification template', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'template_to_delete',
        'channel' => NotificationChannel::WHATSAPP,
        'locale' => 'en',
        'content' => 'Content to delete',
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->notificationTemplateService
        ->setModel($template)
        ->delete();

    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseMissing($this->table, ['id' => $template->id]);
});

test('setModel and getModel methods', function () {
    $template = NotificationTemplate::factory()->create();

    $response = $this->notificationTemplateService->setModel($template);

    expect($response)->toBeInstanceOf(NotificationTemplateService::class)
        ->and($this->notificationTemplateService->getModel())->toBeInstanceOf(NotificationTemplate::class)
        ->and($this->notificationTemplateService->getModel()->id)->toBe($template->id);
});

test('store notification template with different channels', function () {
    $channels = [
        NotificationChannel::MAIL,
        NotificationChannel::SMS,
        NotificationChannel::WHATSAPP,
    ];

    foreach ($channels as $channel) {
        $data = [
            'name' => "test_template_{$channel}",
            'channel' => $channel,
            'locale' => 'en',
            'subject' => $channel === NotificationChannel::MAIL ? "Subject for {$channel}" : null,
            'content' => "Content for {$channel} with {{variable}}",
            'replacements' => ['variable'],
        ];

        $response = $this->notificationTemplateService->store($data);

        expect($response->getModel()->name)->toBe($data['name'])
            ->and($response->getModel()->channel)->toBe($data['channel'])
            ->and($response->getModel()->subject)->toBe($data['subject'])
            ->and($response->getModel()->content)->toBe($data['content']);
    }

    $this->assertDatabaseCount($this->table, 3);
});

test('store notification template with replacements array', function () {
    $data = [
        'name' => 'template_with_replacements',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Order {{order_id}} for {{user_name}}',
        'content' => 'Hello {{user_name}}, your order {{order_id}} totaling {{amount}} is {{status}}.',
        'replacements' => ['user_name', 'order_id', 'amount', 'status'],
        'is_active' => true,
    ];

    $response = $this->notificationTemplateService->store($data);

    expect($response->getModel()->replacements)->toBe($data['replacements']);

    // Test database without JSON field to avoid PostgreSQL comparison issues
    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'subject' => $data['subject'],
        'content' => $data['content'],
        'is_active' => $data['is_active'],
    ]);
});

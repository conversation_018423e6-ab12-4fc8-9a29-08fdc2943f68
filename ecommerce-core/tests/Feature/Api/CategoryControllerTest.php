<?php

use App\Enums\Permission;
use App\Enums\ProductType;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->table = 'categories';
    $this->routeNamePrefix = 'categories';

    // Setup test users with permissions
    $this->users = setupApiControllerTest([
        Permission::CREATE_CATEGORY,
        Permission::EDIT_CATEGORY,
        Permission::DELETE_CATEGORY,
    ], [
        Permission::CREATE_CATEGORY,
        Permission::EDIT_CATEGORY,
        Permission::DELETE_CATEGORY,
    ]);

    $this->adminUser = $this->users['admin_user'];
    $this->staffUser = $this->users['staff_user'];
    $this->customerUser = $this->users['customer_user'];
    $this->unauthorizedUser = $this->users['unauthorized_user'];
});

describe('CategoryController Store', function () {

    test('authorized user can create category', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
            'meta' => [
                'description' => 'Electronic devices and gadgets',
                'keywords' => ['electronics', 'gadgets'],
            ],
            'order_column' => 1,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'slug' => 'electronics',
                'type' => $data['type'],
                'is_active' => $data['is_active'],
                'meta' => $data['meta'],
                'order_column' => $data['order_column'],
            ]);

        $this->assertDatabaseCount($this->table, 1);
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'slug' => 'electronics',
            'type' => $data['type'],
        ]);
    });

    test('can create category with parent', function () {
        Sanctum::actingAs($this->adminUser);

        // Create parent category first
        $parent = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['parent_id'])->toBe($parent->id)
            ->and($response['data']['slug'])->toBe('smartphones');

        $this->assertDatabaseHas($this->table, [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'slug' => 'smartphones',
        ]);
    });

    test('auto-generates unique slug when duplicate name exists', function () {
        Sanctum::actingAs($this->adminUser);

        // Create first category
        Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['slug'])->not()->toBe('electronics')
            ->and($response['data']['slug'])->toStartWith('electronics-');
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422)
            ->and($response['error'])->toHaveKey('name')
            ->and($response['error'])->toHaveKey('type');
    });

    test('validates parent_id exists', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Category',
            'parent_id' => 999, // Non-existent parent
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422)
            ->and($response['error'])->toHaveKey('parent_id');
    });

    test('unauthorized user cannot create category', function () {
        Sanctum::actingAs($this->unauthorizedUser);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveUnauthorizedPermissionResponse();
    });
});

describe('CategoryController Update', function () {

    test('authorized user can update category', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'is_active' => true,
        ]);

        $updateData = [
            'name' => 'Electronics & Technology',
            'type' => ProductType::DEFAULT,
            'is_active' => false,
            'meta' => ['updated' => true],
            'order_column' => 5,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'id' => $category->id,
                'name' => $updateData['name'],
                'slug' => 'electronics-technology',
                'is_active' => $updateData['is_active'],
                'meta' => $updateData['meta'],
                'order_column' => $updateData['order_column'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'name' => 'Electronics & Technology',
            'slug' => 'electronics-technology',
            'is_active' => false,
        ]);
    });

    test('can update category parent', function () {
        Sanctum::actingAs($this->adminUser);

        $parent = Category::factory()->create(['name' => 'Electronics']);
        $category = Category::factory()->create(['name' => 'Smartphones']);

        $updateData = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['parent_id'])->toBe($parent->id);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'parent_id' => $parent->id,
        ]);
    });

    test('prevents category from being its own parent', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create();

        $updateData = [
            'name' => 'Test Category',
            'parent_id' => $category->id, // Self as parent
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $updateData
        )->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422)
            ->and($response['error'])->toHaveKey('parent_id')
            ->and($response['error']['parent_id'][0])->toBe('A category cannot be its own parent.');
    });

    test('unauthorized user cannot update category', function () {
        Sanctum::actingAs($this->unauthorizedUser);

        $category = Category::factory()->create();

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            ['name' => 'Updated Name']
        )->json();

        expect($response)->toHaveUnauthorizedPermissionResponse();
    });
});

describe('CategoryController Destroy', function () {

    test('authorized user can delete category without children', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $category->id]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $category->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $category->id]);
    });

    test('cannot delete category with children', function () {
        Sanctum::actingAs($this->adminUser);

        $parent = Category::factory()->create(['name' => 'Electronics']);
        Category::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $parent->id)
        )->json();

        expect($response)->toHaveFailedGeneralResponse('10001');

        $this->assertDatabaseHas($this->table, ['id' => $parent->id]);
    });

    test('unauthorized user cannot delete category', function () {
        Sanctum::actingAs($this->unauthorizedUser);

        $category = Category::factory()->create();

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $category->id)
        )->json();

        expect($response)->toHaveUnauthorizedPermissionResponse();
    });

    test('returns 404 for non-existent category', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", 999)
        )->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

describe('CategoryController Authorization', function () {

    test('unauthenticated requests are rejected', function () {
        $category = Category::factory()->create();

        $endpoints = [
            ['POST', route("{$this->routeNamePrefix}.store")],
            ['PUT', route("{$this->routeNamePrefix}.update", $category->id)],
            ['DELETE', route("{$this->routeNamePrefix}.destroy", $category->id)],
        ];

        foreach ($endpoints as [$method, $url]) {
            $response = $this->json($method, $url)->json();
            expect($response)->toHaveUnauthenticatedResponse();
        }
    });

    test('staff user with permissions can perform operations', function () {
        Sanctum::actingAs($this->staffUser);

        // Test create
        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'Test Category',
            'type' => ProductType::DEFAULT,
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $category = Category::first();

        // Test update
        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            ['name' => 'Updated Category', 'type' => ProductType::DEFAULT]
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Test delete
        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $category->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();
    });
});

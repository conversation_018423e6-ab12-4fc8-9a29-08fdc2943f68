<?php

use App\Enums\ClassTypePriceType;
use App\Enums\Permission;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\Category;
use App\Models\ClassType;
use App\Models\Product;
use App\Models\TaxClass;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->table = 'class_types';
    $this->routeNamePrefix = 'class-types';

    // Setup test users with permissions
    $this->users = setupApiControllerTest([
        Permission::CREATE_CLASS_TYPE,
        Permission::EDIT_CLASS_TYPE,
        Permission::DELETE_CLASS_TYPE,
    ], [
        Permission::CREATE_CLASS_TYPE,
        Permission::EDIT_CLASS_TYPE,
        Permission::DELETE_CLASS_TYPE,
    ]);

    $this->adminUser = $this->users['admin_user'];
    $this->staffUser = $this->users['staff_user'];
    $this->customerUser = $this->users['customer_user'];
    $this->unauthorizedUser = $this->users['unauthorized_user'];
});

describe('ClassTypeController Store', function () {

    test('authorized user can create class type with required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Yoga Flow',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'colour' => '#96CEB4',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'duration_in_minutes' => $data['duration_in_minutes'],
                'price_type' => $data['price_type'],
                'colour' => $data['colour'],
                'is_addon' => false,
                'is_bookable' => true,
                'is_active' => true,
            ]);

        $this->assertDatabaseCount($this->table, 1);
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
        ]);
    });

    test('can create class type with all optional fields', function () {
        Sanctum::actingAs($this->adminUser);

        $taxClass = TaxClass::factory()->create();

        $data = [
            'name' => 'Advanced Pilates',
            'description' => 'Advanced pilates class for experienced practitioners',
            'duration_in_minutes' => 75,
            'class_count' => 8,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 120.50,
            'tax_class_id' => $taxClass->id,
            'colour' => '#FF6B6B',
            'is_addon' => true,
            'is_bookable' => true,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'description' => $data['description'],
                'duration_in_minutes' => $data['duration_in_minutes'],
                'class_count' => $data['class_count'],
                'price_type' => $data['price_type'],
                'price' => '120.50',
                'tax_class_id' => $taxClass->id,
                'colour' => $data['colour'],
                'is_addon' => $data['is_addon'],
                'is_bookable' => $data['is_bookable'],
                'is_active' => $data['is_active'],
            ]);
    });

    test('can create free class type', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Free Trial Class',
            'duration_in_minutes' => 30,
            'price_type' => ClassTypePriceType::FREE,
            'colour' => '#27AE60',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'price_type' => ClassTypePriceType::FREE,
                'price' => null,
            ]);
    });



    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422)
            ->and($response['error'])->toHaveKeys([
                'name',
                'duration_in_minutes',
                'price_type',
                'colour',
            ]);
    });

    test('validates field formats and constraints', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => str_repeat('a', 256), // Too long
            'duration_in_minutes' => 0, // Too small
            'class_count' => 0, // Too small
            'price_type' => 'invalid_type',
            'price' => -10, // Negative
            'tax_class_id' => 999999, // Non-existent
            'colour' => 'invalid-color', // Invalid format
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422)
            ->and($response['error'])->toHaveKeys([
                'name',
                'duration_in_minutes',
                'class_count',
                'price_type',
                'price',
                'tax_class_id',
                'colour',
            ]);
    });

    test('validates hex color format', function () {
        Sanctum::actingAs($this->adminUser);

        $invalidColors = [
            'red',
            '#FF',
            '#GGGGGG',
            'FF0000',
            '#ff0000g',
        ];

        foreach ($invalidColors as $color) {
            $data = [
                'name' => 'Test Class',
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => $color,
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response['status'])->toBe('ERROR')
                ->and($response['code'])->toBe(422)
                ->and($response['error'])->toHaveKey('colour');
        }
    });

    test('accepts valid hex colors', function () {
        Sanctum::actingAs($this->adminUser);

        $validColors = [
            '#FF0000',
            '#00FF00',
            '#0000FF',
            '#FFFFFF',
            '#000000',
            '#123ABC',
        ];

        foreach ($validColors as $color) {
            $data = [
                'name' => "Test Class {$color}",
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => $color,
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response)->toHaveSuccessGeneralResponse()
                ->and($response['data']['colour'])->toBe($color);
        }
    });
});

describe('ClassTypeController Update', function () {

    test('authorized user can update class type', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create([
            'name' => 'Original Name',
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 50.00,
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'price' => 75.00,
            'is_active' => true,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'id' => $classType->id,
                'name' => $updateData['name'],
                'price' => '75.00',
                'is_active' => $updateData['is_active'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $classType->id,
            'name' => $updateData['name'],
            'price' => 75.00,
            'is_active' => true,
        ]);
    });



    test('can update price type from fixed to free', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create([
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 50.00,
        ]);

        $updateData = [
            'price_type' => ClassTypePriceType::FREE,
            'price' => null,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'price_type' => ClassTypePriceType::FREE,
                'price' => null,
            ]);
    });

    test('validates update data', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create();

        $invalidData = [
            'name' => '', // Empty when required
            'duration_in_minutes' => -5, // Negative
            'price' => -10, // Negative
            'colour' => 'invalid', // Invalid format
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $invalidData
        )->json();

        expect($response['status'])->toBe('ERROR')
            ->and($response['code'])->toBe(422);
    });

    test('returns 404 for non-existent class type', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", 999999),
            ['name' => 'Updated Name']
        );

        expect($response->status())->toBe(404);
    });
});

describe('ClassTypeController Destroy', function () {

    test('authorized user can delete class type', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $classType->id]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $classType->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $classType->id]);
    });

    test('returns 404 for non-existent class type', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", 999999)
        );

        expect($response->status())->toBe(404);
    });

    test('can delete class type with relationships', function () {
        Sanctum::actingAs($this->adminUser);

        $taxClass = TaxClass::factory()->create();
        $classType = ClassType::factory()->create([
            'tax_class_id' => $taxClass->id,
        ]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $classType->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $classType->id]);
        // Tax class should still exist
        $this->assertDatabaseHas('tax_classes', ['id' => $taxClass->id]);
    });
});

describe('ClassTypeController Authorization', function () {

    test('unauthenticated requests are rejected', function () {
        $classType = ClassType::factory()->create();

        $endpoints = [
            ['POST', route("{$this->routeNamePrefix}.store")],
            ['PUT', route("{$this->routeNamePrefix}.update", $classType->id)],
            ['DELETE', route("{$this->routeNamePrefix}.destroy", $classType->id)],
        ];

        foreach ($endpoints as [$method, $url]) {
            $response = $this->json($method, $url)->json();
            expect($response)->toHaveUnauthenticatedResponse();
        }
    });

    test('staff user with permissions can perform operations', function () {
        Sanctum::actingAs($this->staffUser);

        // Test create
        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'Test Class Type',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'colour' => '#FF0000',
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();

        // Test update
        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            ['name' => 'Updated Class Type']
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Test delete
        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $classType->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();
    });

    test('customer user without permissions is forbidden', function () {
        Sanctum::actingAs($this->customerUser);

        $classType = ClassType::factory()->create();

        $endpoints = [
            ['POST', route("{$this->routeNamePrefix}.store"), [
                'name' => 'Test Class Type',
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => '#FF0000',
            ]],
            ['PUT', route("{$this->routeNamePrefix}.update", $classType->id), [
                'name' => 'Updated Name'
            ]],
            ['DELETE', route("{$this->routeNamePrefix}.destroy", $classType->id), []],
        ];

        foreach ($endpoints as [$method, $url, $data]) {
            $response = $this->json($method, $url, $data)->json();
            expect($response)->toHaveUnauthorizedPermissionResponse();
        }
    });

    test('unauthorized staff user is forbidden', function () {
        Sanctum::actingAs($this->unauthorizedUser);

        $classType = ClassType::factory()->create();

        $endpoints = [
            ['POST', route("{$this->routeNamePrefix}.store"), [
                'name' => 'Test Class Type',
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => '#FF0000',
            ]],
            ['PUT', route("{$this->routeNamePrefix}.update", $classType->id), [
                'name' => 'Updated Name'
            ]],
            ['DELETE', route("{$this->routeNamePrefix}.destroy", $classType->id), []],
        ];

        foreach ($endpoints as [$method, $url, $data]) {
            $response = $this->json($method, $url, $data)->json();
            expect($response)->toHaveUnauthorizedPermissionResponse();
        }
    });
});

describe('ClassTypeController Business Logic', function () {

    test('creates class type with default boolean values', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Basic Class',
            'duration_in_minutes' => 45,
            'price_type' => ClassTypePriceType::FIXED,
            'colour' => '#4ECDC4',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'class_count' => 1, // Default value from migration
                'is_addon' => false,
                'is_bookable' => true,
                'is_active' => true,
            ]);

        // Verify in database
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'class_count' => 1,
            'is_addon' => false,
            'is_bookable' => true,
            'is_active' => true,
        ]);
    });

    test('handles boolean field updates correctly', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create([
            'is_addon' => false,
            'is_bookable' => false,
            'is_active' => false,
        ]);

        $updateData = [
            'is_addon' => true,
            'is_bookable' => true,
            'is_active' => true,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray($updateData);
    });

    test('preserves existing data when partially updating', function () {
        Sanctum::actingAs($this->adminUser);

        $classType = ClassType::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original Description',
            'price' => 50.00,
            'is_active' => false,
        ]);

        // Only update name
        $updateData = ['name' => 'Updated Name'];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $updateData
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => 'Updated Name',
                'description' => 'Original Description',
                'price' => '50.00',
                'is_active' => false,
            ]);
    });

    test('handles price type validation with enum values', function () {
        Sanctum::actingAs($this->adminUser);

        $validPriceTypes = [
            ClassTypePriceType::FIXED,
            ClassTypePriceType::FREE,
            ClassTypePriceType::VARIABLE,
        ];

        foreach ($validPriceTypes as $priceType) {
            $data = [
                'name' => "Test Class {$priceType}",
                'duration_in_minutes' => 60,
                'price_type' => $priceType,
                'colour' => '#FF0000',
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response)->toHaveSuccessGeneralResponse()
                ->and($response['data']['price_type'])->toBe($priceType);
        }
    });
});

describe('ClassType Product Synchronization', function () {

    test('automatically creates product when class type is created', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount('products', 0);

        $data = [
            'name' => 'Yoga Flow',
            'description' => 'A dynamic yoga sequence',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => "25.00",
            'colour' => '#96CEB4',
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();

        // Verify product was created
        $this->assertDatabaseCount('products', 1);

        // Verify product has correct attributes synced from class type
        $this->assertDatabaseHas('products', [
            'productable_id' => $classType->id,
            'productable_type' => ClassType::class,
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => $data['price'], // Database stores as decimal, comparison should work
            'type' => ProductType::CLASS_TYPE,
            'status' => ProductStatus::PUBLISH, // is_active = true
        ]);

        // Verify relationship works
        expect($classType->product)->not()->toBeNull()
            ->and($classType->product->name)->toBe($data['name'])
            ->and($classType->product->price)->toBe($data['price']) // Laravel decimal casting returns string
            ->and($classType->product->type)->toBe(ProductType::CLASS_TYPE);
    });

    test('automatically updates product when class type is updated', function () {
        Sanctum::actingAs($this->adminUser);

        // Create class type via API (which should create product)
        $createData = [
            'name' => 'Original Name',
            'description' => 'Original Description',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '50.00',
            'colour' => '#96CEB4',
            'is_active' => true,
        ];

        $createResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $createData)->json();
        expect($createResponse)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();
        $originalProduct = $classType->product;

        expect($originalProduct)->not()->toBeNull();

        // Update class type via API
        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated Description',
            'price' => '75.00',
            'is_active' => false,
        ];

        $updateResponse = $this->putJson(
            route("{$this->routeNamePrefix}.update", $classType->id),
            $updateData
        )->json();

        expect($updateResponse)->toHaveSuccessGeneralResponse();

        // Refresh models
        $classType->refresh();
        $originalProduct->refresh();

        // Verify product was updated (same product, not new one)
        $this->assertDatabaseCount('products', 1);

        // Verify product attributes were synced
        expect($originalProduct->name)->toBe($updateData['name'])
            ->and($originalProduct->description)->toBe($updateData['description'])
            ->and($originalProduct->price)->toBe('75.00') // Laravel decimal casting returns string
            ->and($originalProduct->status)->toBe(ProductStatus::INACTIVE); // is_active = false
    });

    test('automatically deletes product when class type is deleted', function () {
        Sanctum::actingAs($this->adminUser);

        // Create class type via API (which should create product)
        $createData = [
            'name' => 'Test Class for Deletion',
            'duration_in_minutes' => 45,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '40.00',
            'colour' => '#FF6B6B',
        ];

        $createResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $createData)->json();
        expect($createResponse)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();
        $product = $classType->product;

        expect($product)->not()->toBeNull();
        $this->assertDatabaseCount('products', 1);

        // Delete class type via API
        $deleteResponse = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $classType->id)
        )->json();

        expect($deleteResponse)->toHaveSuccessGeneralResponse();

        // Verify both class type and product are soft deleted
        $this->assertSoftDeleted('class_types', ['id' => $classType->id]);
        $this->assertSoftDeleted('products', ['id' => $product->id]);
    });

    test('syncs tax class id to product', function () {
        Sanctum::actingAs($this->adminUser);

        // Create tax class via factory (this is acceptable as it's test data setup)
        $taxClass = TaxClass::factory()->create();

        // Create class type via API with tax class
        $data = [
            'name' => 'Taxable Class',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '30.00',
            'tax_class_id' => $taxClass->id,
            'colour' => '#FF6B6B',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();

        // Verify tax class is synced to product
        expect($classType->product->tax_class_id)->toBe($taxClass->id);

        $this->assertDatabaseHas('products', [
            'productable_id' => $classType->id,
            'tax_class_id' => $taxClass->id,
        ]);
    });

    test('sets product status based on is_active flag', function () {
        Sanctum::actingAs($this->adminUser);

        // Test active class type via API
        $activeData = [
            'name' => 'Active Class',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '25.00',
            'colour' => '#00FF00',
            'is_active' => true,
        ];

        $activeResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $activeData)->json();
        expect($activeResponse)->toHaveSuccessGeneralResponse();

        $activeClassType = ClassType::where('name', 'Active Class')->first();
        expect($activeClassType->product->status)->toBe(ProductStatus::PUBLISH);

        // Test inactive class type via API
        $inactiveData = [
            'name' => 'Inactive Class',
            'duration_in_minutes' => 45,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '20.00',
            'colour' => '#FF0000',
            'is_active' => false,
        ];

        $inactiveResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $inactiveData)->json();
        expect($inactiveResponse)->toHaveSuccessGeneralResponse();

        $inactiveClassType = ClassType::where('name', 'Inactive Class')->first();
        expect($inactiveClassType->product->status)->toBe(ProductStatus::INACTIVE);
    });

    test('can access categories through product relationship', function () {
        Sanctum::actingAs($this->adminUser);

        // Create categories first
        $category1 = Category::factory()->create(['name' => 'Fitness']);
        $category2 = Category::factory()->create(['name' => 'Yoga']);

        // Create class type via API
        $data = [
            'name' => 'Yoga Flow with Categories',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '25.00',
            'colour' => '#96CEB4',
            'category_ids' => [$category1->id, $category2->id],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();
        expect($response)->toHaveSuccessGeneralResponse();

        $classType = ClassType::first();

        // Verify categories are accessible through product
        expect($classType->product)->not()->toBeNull();
        expect($classType->product->categories)->toHaveCount(2);

        // Verify helper method works
        $categories = $classType->getCategories();
        expect($categories)->toHaveCount(2);

        $categoryNames = $categories->pluck('name')->toArray();
        expect($categoryNames)->toContain('Fitness')
            ->and($categoryNames)->toContain('Yoga');
    });
});

<?php

use App\Enums\Permission;
use App\Models\Attachment;
use App\Models\Product;
use App\Models\ProductVariant;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_PRODUCT,
        Permission::EDIT_PRODUCT,
        Permission::DELETE_PRODUCT,
        Permission::VIEW_PRODUCT,
    ], [Permission::VIEW_PRODUCT]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->staffUser = $users->get('staff_user');
    $this->customerUser = $users->get('customer_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // Product-specific setup
    $this->routeNamePrefix = 'products';
    $this->table = resolve(Product::class)->getTable();

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access product endpoints', function () {
    $product = Product::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['product' => $product->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['product' => $product->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create product', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Test Product',
        'description' => 'This is a test product description',
        'price' => 99.99,
        'sale_price' => 79.99,
        'sku' => 'TEST-SKU-001',
        'barcode' => '1234567890123',
        'is_taxable' => true,
        'status' => 'publish',
        'unit' => 'piece',
        'height' => 10.5,
        'width' => 5.2,
        'length' => 15.0,
        'weight' => 2.500,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => '99.99',
            'sale_price' => '79.99',
            'sku' => $data['sku'],
            'barcode' => $data['barcode'],
            'is_taxable' => $data['is_taxable'],
            'status' => $data['status'],
            'unit' => $data['unit'],
            'height' => '10.5',
            'width' => '5.2',
            'length' => '15.0',
            'weight' => '2.500',
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'description' => $data['description'],
        'sku' => $data['sku'],
        'barcode' => $data['barcode'],
        'is_taxable' => $data['is_taxable'],
        'status' => $data['status'],
    ]);
});

test('create product validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
    ]);
});

test('create product validates name length', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => str_repeat('a', 256), // Exceeds max length of 255
        'status' => 'publish',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name may not be greater than 255 characters.'],
    ]);
});

test('create product validates status values', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Product',
        'status' => 'invalid-status',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'status' => ['The selected status is invalid.'],
    ]);
});

test('create product validates price values', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Product',
        'status' => 'publish',
        'price' => -10, // Negative price
        'sale_price' => 150, // Sale price higher than price
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'price' => ['The price must be at least 0.'],
        'sale_price' => ['The sale price must be less than -10.'],
    ]);
});

test('create product validates unique SKU', function () {
    Sanctum::actingAs($this->adminUser);

    // Create a product with a specific SKU
    $existingProduct = Product::factory()->create(['sku' => 'EXISTING-SKU']);

    $data = [
        'name' => 'Test Product',
        'status' => 'publish',
        'sku' => 'EXISTING-SKU', // Duplicate SKU
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'sku' => ['The sku has already been taken.'],
    ]);
});


test('create product with variants', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Product with Variants',
        'status' => 'publish',
        'variants' => [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sale_price' => '40.00',
                'sku' => 'TEST-SMALL',
                'is_active' => true,
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'TEST-LARGE',
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Get the created product ID from response
    $productId = $response->json()['data']['id'];
    $product = Product::find($productId);

    expect($product)->not->toBeNull()
        ->and($product->variants)->toHaveCount(2)
        ->and($product->variants->first()->title)->toBe('Small')
        ->and($product->variants->last()->title)->toBe('Large');
});

test('create product with meta data', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Meta',
        'status' => 'publish',
        'meta' => [
            'featured' => true,
            'tags' => ['electronics', 'gadget'],
            'color' => 'blue',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data']['meta'])->toMatchArray($data['meta']);

    // Get the created product ID from response
    $productId = $response->json()['data']['id'];
    $product = Product::find($productId);

    expect($product)->not->toBeNull()
        ->and($product->meta)->toMatchArray($data['meta']);
});

test('create product validates description length', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Product',
        'status' => 'publish',
        'description' => str_repeat('a', 10001), // Exceeds max length of 10000
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'description' => ['The description may not be greater than 10000 characters.'],
    ]);
});

test('create product validates numeric fields', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Product',
        'status' => 'publish',
        'price' => 'not-a-number',
        'height' => -5,
        'width' => 'invalid',
        'weight' => -1,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'price' => ['The price must be a number.'],
        'height' => ['The height must be at least 0.'],
        'width' => ['The width must be a number.'],
        'weight' => ['The weight must be at least 0.'],
    ]);
});

test('update product', function () {
    Sanctum::actingAs($this->adminUser);

    $product = Product::factory()->create([
        'name' => 'Original Product',
        'description' => 'Original description',
        'price' => 50.00,
        'status' => 'draft',
    ]);

    $data = [
        'name' => 'Updated Product',
        'description' => 'Updated description',
        'price' => 75.00,
        'sale_price' => 60.00,
        'status' => 'publish',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => '75.00',
            'sale_price' => '60.00',
            'status' => $data['status'],
        ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $product->id,
        'name' => $data['name'],
        'description' => $data['description'],
        'status' => $data['status'],
    ]);
});

test('update product validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $product = Product::factory()->create();

    $data = [
        'name' => '', // Empty name
        'status' => '', // Empty status
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
        'status' => ['The status must be a string.', 'The selected status is invalid.'],
    ]);
});

test('update product validates unique SKU excluding current product', function () {
    Sanctum::actingAs($this->adminUser);

    // Create two products with different SKUs
    $product1 = Product::factory()->create(['sku' => 'SKU-001']);
    $product2 = Product::factory()->create(['sku' => 'SKU-002']);

    // Try to update product2 with product1's SKU
    $data = [
        'name' => 'Updated Product',
        'status' => 'publish',
        'sku' => 'SKU-001', // Duplicate SKU
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product2->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'sku' => ['The sku has already been taken.'],
    ]);
});

test('update product allows same SKU for same product', function () {
    Sanctum::actingAs($this->adminUser);

    $product = Product::factory()->create(['sku' => 'SAME-SKU']);

    $data = [
        'name' => 'Updated Product',
        'status' => 'publish',
        'sku' => 'SAME-SKU', // Same SKU as current product
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('update product with variants', function () {
    Sanctum::actingAs($this->adminUser);

    $product = Product::factory()->create();

    // Create existing variant
    $existingVariant = ProductVariant::factory()->create([
        'product_id' => $product->id,
        'title' => 'Original Variant',
        'price' => '30.00',
    ]);

    $data = [
        'name' => 'Updated Product with Variants',
        'status' => 'publish',
        'variants' => [
            [
                'id' => $existingVariant->id,
                'title' => 'Updated Variant',
                'price' => '35.00',
                'is_active' => true,
            ],
            [
                'title' => 'New Variant',
                'price' => '45.00',
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // Verify variants are updated/created
    $product->refresh();
    expect($product->variants)->toHaveCount(2);

    $updatedVariant = $product->variants->where('id', $existingVariant->id)->first();
    expect($updatedVariant->title)->toBe('Updated Variant')
        ->and($updatedVariant->price)->toBe('35.00');
});


test('delete product', function () {
    Sanctum::actingAs($this->adminUser);

    $product = Product::factory()->create([
        'name' => 'Product to Delete',
        'status' => 'publish',
    ]);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['product' => $product->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // Verify product is soft deleted
    $this->assertSoftDeleted($this->table, [
        'id' => $product->id,
    ]);
});

test('delete nonexistent product returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['product' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create product with image', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Image',
        'status' => 'publish',
        'image' => 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Get the created product ID from response
    $productId = $response->json()['data']['id'];
    $product = Product::find($productId);

    expect($product)->not->toBeNull();
    expect($product->getMedia('image'))->toHaveCount(1);
});

test('create product with image and gallery', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Gallery',
        'status' => 'publish',
        'image' => 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        'gallery' => [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png'
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Get the created product ID from response
    $productId = $response->json()['data']['id'];
    $product = Product::find($productId);

    expect($product->getMedia('image'))->toHaveCount(1);

    expect($product)->not->toBeNull();
    expect($product->getMedia('gallery'))->toHaveCount(2);
});


test('create product validates image url format', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Invalid Image',
        'status' => 'publish',
        'image' => 'not-a-valid-url',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'image' => ['The image format is invalid.'],
    ]);
});

test('create product validates gallery url format', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Invalid Gallery',
        'status' => 'publish',
        'gallery' => [
            'https://valid-url.com/image.jpg',
            'not-a-valid-url',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'gallery.1' => ['The gallery.1 format is invalid.'],
    ]);
});

test('create product with variant galleries', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Variant Galleries',
        'status' => 'publish',
        'variants' => [
            [
                'title' => 'Small',
                'price' => '50.00',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ]
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ]
            ]
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Get the created product ID from response
    $productId = $response->json()['data']['id'];
    $product = Product::find($productId);

    expect($product)->not->toBeNull();
    expect($product->variants)->toHaveCount(2);

    // Verify variant galleries
    $smallVariant = $product->variants->where('title', 'Small')->first();
    $largeVariant = $product->variants->where('title', 'Large')->first();

    expect($smallVariant->getMedia('gallery'))->toHaveCount(2);
    expect($largeVariant->getMedia('gallery'))->toHaveCount(1);
});

test('create product validates variant gallery url format', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Product with Invalid Variant Gallery',
        'status' => 'publish',
        'variants' => [
            [
                'title' => 'Small',
                'price' => '50.00',
                'gallery' => [
                    'https://valid-url.com/image.jpg',
                    'not-a-valid-url',
                ]
            ]
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'variants.0.gallery.1' => ['The variants.0.gallery.1 format is invalid.'],
    ]);
});

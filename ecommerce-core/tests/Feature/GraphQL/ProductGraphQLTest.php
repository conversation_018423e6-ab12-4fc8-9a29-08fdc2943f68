<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_PRODUCT, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::STAFF);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_PRODUCT,
        PermissionEnum::CREATE_PRODUCT,
        PermissionEnum::EDIT_PRODUCT,
        PermissionEnum::DELETE_PRODUCT,
    ]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);
});

describe('Product GraphQL Queries', function () {

    test('products query returns paginated list with variants', function () {
        Sanctum::actingAs($this->adminUser);

        // Create products with variants
        $product1 = Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 100.00,
            'sale_price' => 80.00,
            'sku' => 'TEST-001',
            'description' => 'Test product description'
        ]);

        $product2 = Product::factory()->create([
            'name' => 'Test Product 2',
            'price' => 200.00,
            'sku' => 'TEST-002'
        ]);

        // Create variants for product1
        ProductVariant::factory()->create([
            'product_id' => $product1->id,
            'title' => 'Small',
            'price' => 90.00,
            'sku' => 'TEST-001-S'
        ]);

        ProductVariant::factory()->create([
            'product_id' => $product1->id,
            'title' => 'Large',
            'price' => 110.00,
            'sku' => 'TEST-001-L'
        ]);

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                        slug
                        description
                        price
                        sale_price
                        sku
                        barcode
                        is_taxable
                        status
                        created_at
                        updated_at
                        variants {
                            id
                            title
                            price
                            sale_price
                            sku
                            is_active
                        }
                    }
                    paginatorInfo {
                        count
                        currentPage
                        total
                    }
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'products' => [
                    'paginatorInfo' => [
                        'count' => 2,
                        'currentPage' => 1,
                        'total' => 2,
                    ]
                ]
            ]
        ]);

        $productsData = $response->json('data.products.data');
        expect($productsData)->toHaveCount(2);

        // Find product1 in response
        $product1Data = collect($productsData)->firstWhere('id', (string)$product1->id);
        expect($product1Data)->not()->toBeNull();
        expect($product1Data['name'])->toBe('Test Product 1');
        expect($product1Data['price'])->toBe(100);
        expect($product1Data['sale_price'])->toBe(80);
        expect($product1Data['sku'])->toBe('TEST-001');
        expect($product1Data['variants'])->toHaveCount(2);

        // Verify variants
        $variantTitles = collect($product1Data['variants'])->pluck('title')->toArray();
        expect($variantTitles)->toContain('Small', 'Large');
    });

    test('product query returns specific product with relationships', function () {
        Sanctum::actingAs($this->adminUser);

        // Create product
        $product = Product::factory()->create([
            'name' => 'Specific Test Product',
            'price' => 150.00,
            'sku' => 'SPECIFIC-001',
            'description' => 'Specific product for testing'
        ]);

        // Create variant
        $variant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Medium',
            'price' => 140.00,
            'sku' => 'SPECIFIC-001-M'
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    slug
                    description
                    price
                    sale_price
                    sku
                    status
                    variants {
                        id
                        product_id
                        title
                        price
                        sku
                        is_active
                    }
                }
            }
        ', [
            'id' => $product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$product->id,
                    'name' => 'Specific Test Product',
                    'price' => 150,
                    'sku' => 'SPECIFIC-001',
                ]
            ]
        ]);

        $productData = $response->json('data.product');
        expect($productData['variants'])->toHaveCount(1);
        expect($productData['variants'][0]['title'])->toBe('Medium');
        expect($productData['variants'][0]['product_id'])->toBe((string)$product->id);
    });

    test('product query returns media fields', function () {
        Sanctum::actingAs($this->adminUser);

        // Create product with media
        $product = Product::factory()->withImage()->withGallery()->create([
            'name' => 'Product with Media',
            'price' => 200.00,
            'sku' => 'MEDIA-001',
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    image
                    gallery
                }
            }
        ', [
            'id' => $product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$product->id,
                    'name' => 'Product with Media',
                ]
            ]
        ]);

        $productData = $response->json('data.product');
        expect($productData['image'])->not->toBeNull();
        expect($productData['gallery'])->toBeArray();
        expect($productData['gallery'])->toHaveCount(2);
    });

    test('product query returns variant gallery fields', function () {
        Sanctum::actingAs($this->adminUser);

        // Create product with variants that have galleries
        $product = Product::factory()->create([
            'name' => 'Product with Variant Galleries',
            'price' => 200.00,
            'sku' => 'VAR-GALLERY-001',
        ]);

        $variant = ProductVariant::factory()->withGallery()->create([
            'product_id' => $product->id,
            'title' => 'Small Variant',
            'price' => 50.00,
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    variants {
                        id
                        title
                        gallery
                    }
                }
            }
        ', [
            'id' => $product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$product->id,
                    'name' => 'Product with Variant Galleries',
                ]
            ]
        ]);

        $productData = $response->json('data.product');
        expect($productData['variants'])->toHaveCount(1);

        $variantData = $productData['variants'][0];
        expect($variantData['title'])->toBe('Small Variant');
        expect($variantData['gallery'])->toBeArray();
        expect($variantData['gallery'])->toHaveCount(2);
    });

    test('products query with name filter', function () {
        Sanctum::actingAs($this->adminUser);

        Product::factory()->create(['name' => 'Apple iPhone']);
        Product::factory()->create(['name' => 'Samsung Galaxy']);
        Product::factory()->create(['name' => 'Apple iPad']);

        $response = $this->graphQL('
            query {
                products(where: { column: NAME, operator: LIKE, value: "%Apple%" }) {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        $productsData = $response->json('data.products.data');
        expect($productsData)->toHaveCount(2);

        $productNames = collect($productsData)->pluck('name')->toArray();
        expect($productNames)->toContain('Apple iPhone', 'Apple iPad');
        expect($productNames)->not()->toContain('Samsung Galaxy');
    });

    test('products query with price range filter', function () {
        Sanctum::actingAs($this->adminUser);

        Product::factory()->create(['name' => 'Cheap Product', 'price' => 50.00]);
        Product::factory()->create(['name' => 'Mid Product', 'price' => 150.00]);
        Product::factory()->create(['name' => 'Expensive Product', 'price' => 500.00]);

        $response = $this->graphQL('
            query {
                products(where: { AND: [
                    { column: PRICE, operator: GTE, value: 100 }
                    { column: PRICE, operator: LTE, value: 200 }
                ]}) {
                    data {
                        id
                        name
                        price
                    }
                }
            }
        ');

        $productsData = $response->json('data.products.data');
        expect($productsData)->toHaveCount(1);
        expect($productsData[0]['name'])->toBe('Mid Product');
        expect($productsData[0]['price'])->toBe(150);
    });

    test('products query requires view-product permission', function () {
        Sanctum::actingAs($this->customerUser); // Customer without view-product permission

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('product query requires view-product permission', function () {
        Sanctum::actingAs($this->customerUser); // Customer without view-product permission

        $product = Product::factory()->create();

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                }
            }
        ', [
            'id' => $product->id
        ]);

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('products query without authentication returns error', function () {
        Product::factory()->create();

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('products query with ordering by created_at', function () {
        Sanctum::actingAs($this->adminUser);

        $product1 = Product::factory()->create(['name' => 'First Product']);
        sleep(1);
        $product2 = Product::factory()->create(['name' => 'Second Product']);

        $response = $this->graphQL('
            query {
                products(orderBy: { column: CREATED_AT, order: DESC }) {
                    data {
                        id
                        name
                        created_at
                    }
                }
            }
        ');

        $productsData = $response->json('data.products.data');
        expect($productsData)->toHaveCount(2);

        // Should be ordered by created_at DESC (newest first)
        expect((int)$productsData[0]['id'])->toBe($product2->id);
        expect((int)$productsData[1]['id'])->toBe($product1->id);
    });
});

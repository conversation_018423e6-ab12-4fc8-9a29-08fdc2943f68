<?php

namespace App\Services;

use App\Repositories\ProductVariantRepository;

class ProductVariantService extends BaseService
{
    public function __construct(
        protected ProductVariantRepository $productVariantRepository
    ) {
        parent::__construct($productVariantRepository);
    }

    /**
     * Get model with relationships loaded
     */
    public function getModel()
    {
        $this->model->loadMissing(['product']);
        return parent::getModel();
    }
}

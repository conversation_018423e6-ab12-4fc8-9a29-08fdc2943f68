<?php

namespace App\Services;


class BaseService
{
    protected $model;
    protected $repository;

    public function store(array $data): static
    {
        $model = $this->repository->create($data);
        $this->setModel($model);

        return $this;
    }

    public function update(array $data): static
    {
        $model = $this->repository->update($data, $this->model->id);

        $this->setModel($model);

        return $this;
    }

    public function delete(): static
    {
        $this->repository->delete($this->model->id);

        return $this;
    }

    public function setModel($model): static
    {
        $this->model = $model;

        return $this;
    }

    public function getModel()
    {
        return $this->model;
    }
}

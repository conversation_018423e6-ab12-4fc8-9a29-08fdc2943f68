<?php

namespace App\Services;

use App\Repositories\ProductRepository;
use App\Repositories\ProductVariantRepository;

class ProductService extends BaseService
{
    public function __construct(
        ProductRepository $product_repository,
        protected ProductVariantRepository $productVariantRepository
    ) {
        $this->repository = $product_repository;
    }

    /**
     * Create or update product variants
     */
    public function syncVariants(array $variants): static
    {
        if (empty($variants)) {
            return $this;
        }

        foreach ($variants as $variantData) {
            $galleryUrls = $variantData['gallery'] ?? [];

            // Remove gallery from variant data since it's not fillable
            unset($variantData['gallery']);

            if (isset($variantData['id'])) {
                // Update existing variant
                $variantModel = $this->productVariantRepository->update($variantData, $variantData['id']);
                $variantModel = $this->productVariantRepository->find($variantData['id']);
            } else {
                // Create new variant
                $variantData['product_id'] = $this->model->id;
                $variantModel = $this->productVariantRepository->create($variantData);
            }

            // Set gallery for the variant
            $this->productVariantRepository->setGallery($variantModel, $galleryUrls);
        }

        return $this;
    }

    public function setImage(?string $url = null): static
    {
        if (!$url) {
            return $this;
        }

        $this->repository->setImage($this->model, $url);

        return $this;
    }

    public function setGallery(array $urls = []): static
    {
        if (empty($urls)) {
            return $this;
        }

        $this->repository->setGallery($this->model, $urls);

        return $this;
    }


    /**
     * Get model with relationships loaded
     */
    public function getModel()
    {
        $this->model->loadMissing(['variants', 'type']);
        return parent::getModel();
    }
}

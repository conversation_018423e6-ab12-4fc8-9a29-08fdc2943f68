<?php

namespace App\Repositories;

use App\Models\Product;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;

class ProductRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model(): string
    {
        return Product::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    public function setImage(Product $product, ?string $url = null): static
    {
        if (!$url) {
            return $this;
        }

        $product->addMediaFromUrl($url)
            ->toMediaCollection('image');

        return $this;
    }

    public function setGallery(Product $product, array $urls = []): static
    {
        if (empty($urls)) {
            return $this;
        }

        // Clear existing gallery images
        $product->clearMediaCollection('gallery');

        // Add new gallery images
        foreach ($urls as $url) {
            if (!empty($url)) {
                $product->addMediaFromUrl($url)
                    ->toMediaCollection('gallery');
            }
        }

        return $this;
    }
}

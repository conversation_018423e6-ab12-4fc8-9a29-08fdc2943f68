<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\PresignedUrlRequest;
use App\Http\Resources\ApiResponse;

class AttachmentController extends Controller
{
    /**
     * Generate S3 presigned URL for direct upload
     *
     * @param PresignedUrlRequest $request
     * @return JsonResponse
     */
    public function generatePresignedUrl(PresignedUrlRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Handle multiple files (array only)
        $presignedUrls = [];

        foreach ($input['media'] as $filename) {
            $presignedUrls[] = $this->generateSinglePresignedUrl($filename);
        }

        return (new ApiResponse())
            ->setData($presignedUrls)
            ->getResponse();
    }

    /**
     * Generate presigned URL for a single file
     *
     * @param string $filename
     * @return array
     */
    private function generateSinglePresignedUrl(string $filename): array
    {
        // Generate unique file key while preserving the sanitized filename
        $fileExtension = pathinfo($filename, PATHINFO_EXTENSION);
        $filenameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);

        // Create unique filename by prepending timestamp and unique ID to preserve original name
        $uniqueFilename = uniqid() . '_' . $filenameWithoutExt . '.' . $fileExtension;
        $key = 'uploads/' . date('Y/m/d') . '/' . $uniqueFilename;



        // Use Laravel's temporaryUploadUrl method for S3 with public-read ACL and proper Content-Type
        ['url' => $uploadUrl, 'headers' => $headers] = Storage::disk('s3')->temporaryUploadUrl(
            $key,
            now()->addMinutes(15),
            [
                'ACL' => 'public-read'
            ]
        );

        // Generate the permanent file URL
        $fileUrl = Storage::disk('s3')->url($key);

        return [
            'upload_url' => $uploadUrl,
            'file_url' => $fileUrl,
            'key' => $key,
            'expires_in' => 900, // 15 minutes in seconds
            'method' => 'PUT',
            'headers' => $headers,
            'original_name' => $filename
        ];
    }
}

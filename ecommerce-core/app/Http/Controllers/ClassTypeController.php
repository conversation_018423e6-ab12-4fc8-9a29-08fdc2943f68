<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClassType\CreateClassTypeRequest;
use App\Http\Requests\ClassType\UpdateClassTypeRequest;
use App\Http\Resources\ApiResponse;
use App\Models\ClassType;
use App\Services\ClassTypeService;
use Illuminate\Http\JsonResponse;

class ClassTypeController extends Controller
{
    public function __construct(protected ClassTypeService $classTypeService) {}

    /**
     * Store a newly created class type.
     *
     * @param CreateClassTypeRequest $request
     * @return JsonResponse
     */
    public function store(CreateClassTypeRequest $request): JsonResponse
    {
        $input = $request->validated();

        $classType = $this->classTypeService
            ->store($input)
            ->getModel()
            ->refresh();

        $this->classTypeService
            ->setModel($classType)
            ->syncCategories($input['category_ids'] ?? []);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($classType)
            ->getResponse();
    }

    /**
     * Update the specified class type.
     *
     * @param UpdateClassTypeRequest $request
     * @param ClassType $classType
     * @return JsonResponse
     */
    public function update(UpdateClassTypeRequest $request, ClassType $classType): JsonResponse
    {
        $input = $request->validated();

        $classType = $this->classTypeService
            ->setModel($classType)
            ->update($input)
            ->getModel();

        // Sync categories if provided
        if (isset($input['category_ids'])) {
            $this->classTypeService
                ->setModel($classType)
                ->syncCategories($input['category_ids']);
        }

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($classType)
            ->getResponse();
    }

    /**
     * Remove the specified class type.
     *
     * @param ClassType $classType
     * @return JsonResponse
     */
    public function destroy(ClassType $classType): JsonResponse
    {
        $this->classTypeService
            ->setModel($classType)
            ->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}

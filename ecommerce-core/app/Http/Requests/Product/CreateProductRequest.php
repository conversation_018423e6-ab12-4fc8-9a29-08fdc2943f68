<?php

namespace App\Http\Requests\Product;

use App\Enums\TypeEnum;
use App\Helpers\SanitizesInput;
use BenSampo\Enum\Rules\EnumValue;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:10000'],
            'price' => ['nullable', 'numeric', 'min:0'],
            'sale_price' => ['nullable', 'numeric', 'min:0', 'lt:price'],
            'sku' => ['nullable', 'string', 'max:255', Rule::unique('products', 'sku')->withoutTrashed()],
            'barcode' => ['nullable', 'string', 'max:255'],
            'is_taxable' => ['boolean'],
            'tax_class_id' => ['nullable', 'integer'],
            'shipping_class_id' => ['nullable', 'integer'],
            'meta' => ['nullable', 'array'],
            'status' => ['string', 'in:publish,draft,inactive'],
            'unit' => ['nullable', 'string', 'max:50'],
            'height' => ['nullable', 'numeric', 'min:0'],
            'width' => ['nullable', 'numeric', 'min:0'],
            'length' => ['nullable', 'numeric', 'min:0'],
            'weight' => ['nullable', 'numeric', 'min:0'],
            'variants' => ['nullable', 'array'],
            'variants.*.title' => ['required_with:variants', 'string', 'max:255'],
            'variants.*.price' => ['required_with:variants', 'string'],
            'variants.*.sale_price' => ['nullable', 'string'],
            'variants.*.sku' => ['nullable', 'string', 'max:255'],
            'variants.*.barcode' => ['nullable', 'string', 'max:255'],
            'variants.*.is_active' => ['boolean'],
            'variants.*.width' => ['nullable', 'numeric', 'min:0'],
            'variants.*.height' => ['nullable', 'numeric', 'min:0'],
            'variants.*.length' => ['nullable', 'numeric', 'min:0'],
            'variants.*.weight' => ['nullable', 'numeric', 'min:0'],
            'variants.*.gallery' => ['nullable', 'array'],
            'variants.*.gallery.*' => ['string', 'url'],
            'image' => ['nullable', 'string', 'url'],
            'gallery' => ['nullable', 'array'],
            'gallery.*' => ['string', 'url'],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'name' => $this->name ? SanitizesInput::sanitizeName($this->name) : $this->name,
            'slug' => $this->slug ? trim($this->slug) : $this->slug,
            'sku' => $this->sku ? strtoupper(trim($this->sku)) : $this->sku,
            'barcode' => $this->barcode ? trim($this->barcode) : $this->barcode,
        ]);
    }
}

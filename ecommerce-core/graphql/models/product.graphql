extend type Query {
  product(id: ID! @eq): Product
    @hasPermission(permission: "view-product")
    @find(model: "App\\Models\\Product")

  products(
    where: _ @whereConditions(columns: ["name", "price", "sale_price"])
    orderBy: _ @orderBy(columns: ["id", "name", "created_at", "updated_at"])
  ): [Product!]!
    @hasPermission(permission: "view-product")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Product {
  id: ID!
  name: String!
  slug: String!
  description: String
  price: Float
  sale_price: Float
  sku: String
  barcode: String
  is_taxable: Boolean
  tax_class_id: ID
  shipping_class_id: ID
  meta: JSON
  status: String
  unit: String
  height: Float
  width: Float
  length: Float
  weight: Float
  image: String @mediaUrl(collection: "image")
  gallery: [String!] @mediaUrl(collection: "gallery", multiple: true)
  created_at: DateTime
  updated_at: DateTime

  variants: [ProductVariant!]! @hasMany
  categories: [Category!]! @belongsToMany

  # Polymorphic relationship to the source model (ClassType, Service, etc.)
  productable: Productable @morphTo
}

type ProductVariant {
  id: ID!
  product_id: ID!
  title: String!
  price: Float
  sale_price: Float
  sku: String
  barcode: String
  is_active: Boolean
  width: Float
  height: Float
  length: Float
  weight: Float
  options: JSON
  gallery: [String!] @mediaUrl(collection: "gallery", multiple: true)
  created_at: DateTime
  updated_at: DateTime
}

# Union type for polymorphic relationships
union Productable = ClassType

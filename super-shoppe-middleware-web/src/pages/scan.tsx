import React, { useState, useRef, useEffect } from 'react';
import {
  CapacitorBarcodeScanner,
  CapacitorBarcodeScannerTypeHint,
} from '@capacitor/barcode-scanner';
import { useRouter } from 'next/router';
import MobileHeader from '@components/ui/mobile-header';
import { fetchOrders } from '../data-graphql/orders';
import { OrderColumn } from '../data-graphql/graphql-columns';
import { createDynamicFilter, eq } from '../utils/graphql-query-builder';
import { playSuccessSound, playErrorSound } from '@utils/audio-feedback';
import { toast } from 'react-toastify';
import Button from '@components/ui/button';

interface ScanHistoryItem {
  id: number;
  code: string;
  orderInfo: string;
  time: string;
  status: 'found' | 'not_found';
}

const ScanPage = () => {
  const router = useRouter();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const [scanHistory, setScanHistory] = useState<ScanHistoryItem[]>([
    {
      id: 1,
      code: 'ORD-AB123',
      orderInfo: 'Order #ORD-AB123 - Found',
      time: '10:30 AM',
      status: 'found',
    },
    {
      id: 2,
      code: 'ORD-XY789',
      orderInfo: 'Order #ORD-XY789 - Found',
      time: '10:25 AM',
      status: 'found',
    },
    {
      id: 3,
      code: 'INVALID-123',
      orderInfo: 'Order not found',
      time: '10:20 AM',
      status: 'not_found',
    },
  ]);
  const [error, setError] = useState<string>('');
  const [searchingOrder, setSearchingOrder] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Search for order by display_id
  const searchOrderByDisplayId = async (displayId: string) => {
    setSearchingOrder(true);
    try {
      // Use the proper GraphQL query structure with where conditions
      const data = await fetchOrders({
        where: createDynamicFilter([eq(OrderColumn.DISPLAY_ID, displayId)]),
        first: 1,
      });

      if (data?.orders?.data && data.orders.data.length > 0) {
        const order = data.orders.data[0];

        // Play success sound
        playSuccessSound();

        // Show success message
        toast.success(`Order ${displayId} found! Redirecting...`);

        // Add to scan history
        const newScan: ScanHistoryItem = {
          id: scanHistory.length + 1,
          code: displayId,
          orderInfo: `Order #${displayId} - Found`,
          time: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          status: 'found',
        };
        setScanHistory([newScan, ...scanHistory]);

        // Redirect to order details page
        setTimeout(() => {
          router.push(`/orders/${order.id}`);
        }, 1000);

        return true;
      } else {
        // Order not found
        playErrorSound();
        toast.error(`Order ${displayId} not found!`);

        // Add to scan history
        const newScan: ScanHistoryItem = {
          id: scanHistory.length + 1,
          code: displayId,
          orderInfo: 'Order not found',
          time: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          status: 'not_found',
        };
        setScanHistory([newScan, ...scanHistory]);

        return false;
      }
    } catch (error) {
      console.error('Error searching for order:', error);
      playErrorSound();
      toast.error('Error searching for order. Please try again.');
      setError('Failed to search for order. Please try again.');
      return false;
    } finally {
      setSearchingOrder(false);
    }
  };

  // Check and request camera permissions
  const checkPermissions = async () => {
    try {
      return true;
    } catch (err) {
      console.error('Permission check error:', err);
      setError('Camera permission is required to scan codes.');
      return false;
    }
  };

  const handleStartScan = async () => {
    try {
      // Check permissions first
      const hasPermission = await checkPermissions();
      if (!hasPermission) {
        return;
      }

      setIsScanning(true);
      setError('');

      // Start the code scanner
      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: CapacitorBarcodeScannerTypeHint.ALL,
        scanInstructions: 'Scan order barcode (e.g., ORD-AB123)',
        scanButton: false,
        scanText: 'Scan',
        cameraDirection: 1, // BACK camera
        scanOrientation: 3, // ADAPTIVE
      });

      if (result.ScanResult) {
        setScannedCode(result.ScanResult);

        // Search for order with the scanned code
        await searchOrderByDisplayId(result.ScanResult);
      }
    } catch (err) {
      console.error('Barcode scan error:', err);
      setError('Failed to scan code. Please try again.');
    } finally {
      setIsScanning(false);
    }
  };

  const handleManualInput = async () => {
    const code = prompt('Enter order display ID (e.g., ORD-AB123):');
    if (code) {
      setScannedCode(code);
      setError('');

      // Search for order with the entered code
      await searchOrderByDisplayId(code);
    }
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Note: For a production app, you would need to implement image processing
      // to extract QR/barcode data from the uploaded image using libraries like
      // jsQR or QuaggaJS. For now, we'll simulate the scan.
      // For demo purposes, generate a mock order code
      const mockCode = `ORD-${Math.random()
        .toString(36)
        .substr(2, 5)
        .toUpperCase()}`;
      setScannedCode(mockCode);
      setError('');

      // Search for order with the mock code (will likely not be found)
      await searchOrderByDisplayId(mockCode);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <MobileHeader
        title="Scan Order"
        showBack={false}
        rightComponent={
          <Button
            onClick={() => setScanHistory([])}
            variant="error"
            className="text-sm"
          >
            Clear History
          </Button>
        }
      />

      {/* Current Scan Result */}
      {scannedCode && (
        <div className="px-4 pt-4">
          <div
            className={`border rounded-lg p-4 ${
              searchingOrder
                ? 'bg-blue-50 border-blue-200'
                : 'bg-green-50 border-green-200'
            }`}
          >
            <h3
              className={`font-medium mb-2 ${
                searchingOrder ? 'text-blue-800' : 'text-green-800'
              }`}
            >
              {searchingOrder ? 'Searching Order...' : 'Last Scanned'}
            </h3>
            <p
              className={`font-mono text-sm break-all ${
                searchingOrder ? 'text-blue-700' : 'text-green-700'
              }`}
            >
              {scannedCode}
            </p>
            {searchingOrder && (
              <div className="flex items-center space-x-2 mt-2">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-blue-700 text-sm">
                  Searching for order...
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Scanner Section */}
      <div className="px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="text-center">
            {isScanning ? (
              <div className="py-12">
                <div className="mx-auto w-32 h-32 border-4 border-blue-600 border-dashed rounded-lg flex items-center justify-center mb-4 animate-pulse">
                  <svg
                    className="w-16 h-16 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 mb-2">
                  Camera is active - scanning for orders...
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Point your camera at an order barcode (e.g., ORD-AB123)
                </p>
                <Button
                  onClick={() => setIsScanning(false)}
                  variant="error"
                  className="px-6"
                >
                  Cancel Scan
                </Button>
              </div>
            ) : (
              <div className="py-8">
                <div className="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <svg
                    className="w-12 h-12 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Order Scanner
                </h3>
                <p className="text-gray-600 mb-4">
                  Scan order barcodes to view order details
                </p>
                <div className="text-xs text-gray-500 mb-6 space-y-1">
                  <p>• Scan order display IDs (e.g., ORD-AB123)</p>
                  <p>• Automatically redirects to order details if found</p>
                  <p>• Camera permission required for scanning</p>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={handleStartScan}
                    className="w-full font-medium"
                  >
                    Start Camera Scan
                  </Button>

                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      onClick={handleManualInput}
                      variant="border"
                      className="text-sm"
                    >
                      Manual Input
                    </Button>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="border"
                      className="text-sm"
                    >
                      Upload Image
                    </Button>
                  </div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <svg
                className="w-5 h-5 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h4 className="font-medium text-red-800">Error</h4>
            </div>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {/* Last Scanned Result */}
        {scannedCode && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <svg
                className="w-5 h-5 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <h4 className="font-medium text-green-800">Last Scanned</h4>
            </div>
            <p className="text-green-700 font-mono text-sm">{scannedCode}</p>
          </div>
        )}

        {/* Scan History */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Scan History
          </h3>
          {scanHistory.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No scans yet</p>
          ) : (
            <div className="space-y-3">
              {scanHistory.map((scan) => (
                <div
                  key={scan.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded ${
                        scan.status === 'found'
                          ? 'bg-green-100 text-green-600'
                          : 'bg-red-100 text-red-600'
                      }`}
                    >
                      {scan.status === 'found' ? (
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      )}
                    </div>
                    <div>
                      <p
                        className={`font-medium ${
                          scan.status === 'found'
                            ? 'text-gray-900'
                            : 'text-red-700'
                        }`}
                      >
                        {scan.orderInfo}
                      </p>
                      <p className="text-sm text-gray-600 font-mono">
                        {scan.code}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">{scan.time}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScanPage;

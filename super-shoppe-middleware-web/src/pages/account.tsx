import React from 'react';
import { useRouter } from 'next/router';
import { useCustomer } from '@contexts/customer.context';
import { removeToken } from '@framework/utils/get-token';
import { STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import { useUI } from '@contexts/ui.context';
import { ROUTES } from '@utils/routes';

const AccountPage = () => {
  const router = useRouter();
  const { customer } = useCustomer();
  const { unauthorize } = useUI();

  const handleLogout = async () => {
    // Clear tokens and redirect to login
    removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
    removeToken(STORAGE_KEY_ENUM.GUEST_TOKEN);
    unauthorize();
    router.push(ROUTES.LOGIN);
  };

  const menuItems = [
    {
      icon: (
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      title: 'About',
      subtitle: 'App version and information',
      action: () => alert('About - Version 1.0.0'),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm px-4 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">Account</h1>
        </div>
      </div>

      {/* Profile Section */}
      <div className="px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-gray-900">
                {customer?.firstname} {customer?.lastname}
              </h2>
              <p className="text-gray-600">{customer?.email}</p>
              <span
                className={`inline-flex mt-1 px-2 py-1 text-xs rounded ${
                  customer?.is_active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {customer?.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
          {menuItems.map((item, index) => (
            <button
              key={index}
              onClick={item.action}
              className="w-full flex items-center space-x-4 p-4 hover:bg-gray-50 transition border-b border-gray-100 last:border-b-0"
            >
              <div className="text-gray-400">{item.icon}</div>
              <div className="flex-1 text-left">
                <h4 className="font-medium text-gray-900">{item.title}</h4>
                <p className="text-sm text-gray-600">{item.subtitle}</p>
              </div>
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          ))}
        </div>

        {/* Logout Button */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-4 p-4 hover:bg-red-50 transition text-red-600"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            <div className="flex-1 text-left">
              <h4 className="font-medium">Sign Out</h4>
              <p className="text-sm text-red-500">Sign out of your account</p>
            </div>
          </button>
        </div>

        {/* App Version */}
        <div className="text-center mt-6 text-gray-500 text-sm">
          <p>Inventory Management App</p>
          <p>Version 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;

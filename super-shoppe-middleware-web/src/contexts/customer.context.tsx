import { useCustomerQuery } from '../data-graphql/customer';
import React, { useMemo, useEffect } from 'react';
import { useRouter } from 'next/router';
import { getToken } from '@framework/utils/get-token';
import { ROUTES } from '@utils/routes';
import { PageLoader } from '@components/ui/loaders';
import { removeToken } from '@framework/utils/get-token';
import { STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import { toast } from 'react-toastify';
export interface State {
  customerLoading: boolean;
  customer: any;
  authChecking: boolean;
  isAuthenticated: boolean;
}

const initialState = {
  customerLoading: false,
  customer: null,
  authChecking: true,
  isAuthenticated: false,
};

type Action =
  | {
      type: 'UPDATE_CUSTOMER_LOADING';
      payload: boolean;
    }
  | {
      type: 'UPDATE_CUSTOMER';
      payload: any;
    }
  | {
      type: 'SET_AUTH_CHECKING';
      payload: boolean;
    }
  | {
      type: 'SET_AUTHENTICATED';
      payload: boolean;
    };

export const CustomerContext = React.createContext<State | any>(initialState);

CustomerContext.displayName = 'CustomerContext';

function customerReducer(state: State, action: Action) {
  switch (action.type) {
    case 'UPDATE_CUSTOMER_LOADING': {
      return {
        ...state,
        customerLoading: action.payload,
      };
    }
    case 'UPDATE_CUSTOMER': {
      return {
        ...state,
        customer: action.payload,
      };
    }
    case 'SET_AUTH_CHECKING': {
      return {
        ...state,
        authChecking: action.payload,
      };
    }
    case 'SET_AUTHENTICATED': {
      return {
        ...state,
        isAuthenticated: action.payload,
      };
    }
  }
}

export const CustomerProvider: React.FC = ({ children }) => {
  const router = useRouter();
  const { data, isLoading, error } = useCustomerQuery();
  const [state, dispatch] = React.useReducer(customerReducer, initialState);

  // List of public routes that don't require authentication
  const publicRoutes = [ROUTES.LOGIN, ROUTES.SIGN_UP];
  const isPublicRoute = publicRoutes.includes(router.pathname);

  const updateCustomerLoading = (payload: boolean) =>
    dispatch({ type: 'UPDATE_CUSTOMER_LOADING', payload });
  const updateCustomer = (payload: any) =>
    dispatch({ type: 'UPDATE_CUSTOMER', payload });
  const setAuthChecking = (payload: boolean) =>
    dispatch({ type: 'SET_AUTH_CHECKING', payload });
  const setAuthenticated = (payload: boolean) =>
    dispatch({ type: 'SET_AUTHENTICATED', payload });

  // Local validation function
  const validateCustomerAccount = (customer: any): boolean => {
    if (!customer) {
      return false;
    }

    // Check if customer is active
    if (!customer.is_active) {
      handleAccountViolation(
        'Your account has been deactivated. Please contact support.'
      );
      return false;
    }

    // Check if customer is blocked
    if (customer.is_book_blocked) {
      handleAccountViolation('Your account has been temporarily blocked.');
      return false;
    }

    return true;
  };

  // Handle account violations by clearing auth and showing message
  const handleAccountViolation = (message: string) => {
    removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
    setAuthenticated(false);
    toast.error(message);
  };

  React.useEffect(() => {
    updateCustomerLoading(isLoading);
  }, [isLoading]);

  React.useEffect(() => {
    if (data) {
      updateCustomer(data.me ?? null);
    }
  }, [data]);

  // Simplified authentication effect
  useEffect(() => {
    const checkAuth = async () => {
      const token = await getToken();

      // If user has token but is on login page, redirect to home
      if (token && router.pathname === ROUTES.LOGIN) {
        router.replace('/');
        return;
      }

      // If no token and trying to access protected route, redirect to login
      if (!token && !isPublicRoute) {
        router.replace(ROUTES.LOGIN);
        return;
      }

      // Set authentication state based on token presence
      setAuthenticated(!!token);

      // Set loading state based on whether we're waiting for customer data
      if (token && !isPublicRoute) {
        setAuthChecking(isLoading);
      } else {
        setAuthChecking(false);
      }
    };

    checkAuth();
  }, [router.pathname, isPublicRoute, isLoading]);

  const value = useMemo(
    () => ({
      ...state,
      updateCustomerLoading,
      updateCustomer,
      setAuthChecking,
      setAuthenticated,
      validateCustomerAccount,
    }),
    [state]
  );

  // Show loading state while checking authentication
  if (state.authChecking && !isPublicRoute) {
    return <PageLoader text="Authenticating..." size="lg" />;
  }

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
};

export const useCustomer = () => {
  const context = React.useContext(CustomerContext);
  if (context === undefined) {
    throw new Error(`useCustomer must be used within a CustomerProvider`);
  }
  return context;
};

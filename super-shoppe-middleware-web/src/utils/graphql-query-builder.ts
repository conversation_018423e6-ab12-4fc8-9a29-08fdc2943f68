// Base types for GraphQL operations
export interface WhereCondition {
  column: string;
  operator: SQLOperator;
  value: string | number | boolean | null;
}

export interface WhereConditions {
  AND?: WhereCondition[];
  OR?: WhereCondition[];
}

export interface OrderByClause {
  column: string;
  order: 'ASC' | 'DESC';
}

export interface BaseQueryVariables {
  first?: number;
  page?: number;
  where?: WhereConditions;
  orderBy?: OrderByClause[];
}

export enum SQLOperator {
  EQ = 'EQ',
  NEQ = 'NEQ',
  GT = 'GT',
  GTE = 'GTE',
  LT = 'LT',
  LTE = 'LTE',
  LIKE = 'LIKE',
  NLIKE = 'NLIKE',
  ILIKE = 'ILIKE',
  NILIKE = 'NILIKE',
  IN = 'IN',
  NIN = 'NIN',
  IS_NULL = 'IS_NULL',
  IS_NOT_NULL = 'IS_NOT_NULL',
}

// Filter condition interface
export interface FilterCondition {
  column: string;
  operator: SQLOperator;
  value?: string | number | boolean | null | any[];
}

// Complex filter interface for AND/OR operations
export interface ComplexFilter {
  AND?: FilterCondition[];
  OR?: FilterCondition[];
}

// Main dynamic filter function
export const createDynamicFilter = (
  conditions: FilterCondition[]
): ComplexFilter => {
  if (conditions.length === 0) return {};

  return {
    AND: conditions,
  };
};

// Advanced filter with AND/OR support
export const createComplexFilter = (config: {
  AND?: FilterCondition[];
  OR?: FilterCondition[];
}): ComplexFilter => {
  const filter: ComplexFilter = {};

  if (config.AND && config.AND.length > 0) {
    filter.AND = config.AND;
  }

  if (config.OR && config.OR.length > 0) {
    filter.OR = config.OR;
  }

  return filter;
};

// Helper functions for common operations
export const eq = (column: string, value: any): FilterCondition => ({
  column,
  operator: SQLOperator.EQ,
  value,
});

export const neq = (column: string, value: any): FilterCondition => ({
  column,
  operator: SQLOperator.NEQ,
  value,
});

export const like = (column: string, value: string): FilterCondition => ({
  column,
  operator: SQLOperator.LIKE,
  value,
});

export const ilike = (column: string, value: string): FilterCondition => ({
  column,
  operator: SQLOperator.ILIKE,
  value,
});

export const gte = (
  column: string,
  value: number | string
): FilterCondition => ({
  column,
  operator: SQLOperator.GTE,
  value,
});

export const lte = (
  column: string,
  value: number | string
): FilterCondition => ({
  column,
  operator: SQLOperator.LTE,
  value,
});

export const gt = (
  column: string,
  value: number | string
): FilterCondition => ({
  column,
  operator: SQLOperator.GT,
  value,
});

export const lt = (
  column: string,
  value: number | string
): FilterCondition => ({
  column,
  operator: SQLOperator.LT,
  value,
});

export const inArray = (column: string, values: any[]): FilterCondition => ({
  column,
  operator: SQLOperator.IN,
  value: values,
});

export const notInArray = (column: string, values: any[]): FilterCondition => ({
  column,
  operator: SQLOperator.NIN,
  value: values,
});

export const isNull = (column: string): FilterCondition => ({
  column,
  operator: SQLOperator.IS_NULL,
});

export const isNotNull = (column: string): FilterCondition => ({
  column,
  operator: SQLOperator.IS_NOT_NULL,
});

// Convenience functions for common patterns
export const search = (column: string, term: string): FilterCondition =>
  like(column, `%${term}%`);

export const startsWith = (column: string, term: string): FilterCondition =>
  like(column, `${term}%`);

export const endsWith = (column: string, term: string): FilterCondition =>
  like(column, `%${term}`);

export const between = (
  column: string,
  min: number | string,
  max: number | string
): FilterCondition[] => [gte(column, min), lte(column, max)];

// Build conditions helper function
export const buildConditions = (filters: {
  [key: string]: any;
}): FilterCondition[] => {
  const conditions: FilterCondition[] = [];

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      // Handle different types of filters
      if (typeof value === 'string' && key.includes('search')) {
        // Search fields use LIKE operator
        conditions.push(search(key.replace('_search', ''), value));
      } else if (Array.isArray(value) && value.length > 0) {
        // Array values use IN operator
        conditions.push(inArray(key, value));
      } else {
        // Default to equality
        conditions.push(eq(key, value));
      }
    }
  });

  return conditions;
};

// Order builder helper
export const buildOrderBy = (
  orderBy?: string,
  sortedBy?: 'ASC' | 'DESC'
): OrderByClause[] => {
  if (!orderBy) return [];

  return [
    {
      column: orderBy.toUpperCase(),
      order: sortedBy || 'DESC',
    },
  ];
};

// Query variables builder
export const buildQueryVariables = (params: {
  first?: number;
  page?: number;
  filters?: { [key: string]: any };
  orderBy?: string;
  sortedBy?: 'ASC' | 'DESC';
}): BaseQueryVariables => {
  const { first = 15, page = 1, filters = {}, orderBy, sortedBy } = params;

  const conditions = buildConditions(filters);
  const whereConditions =
    conditions.length > 0 ? createDynamicFilter(conditions) : undefined;
  const orderByClause = buildOrderBy(orderBy, sortedBy);

  return {
    first,
    page,
    where: whereConditions,
    orderBy: orderByClause.length > 0 ? orderByClause : undefined,
  };
};

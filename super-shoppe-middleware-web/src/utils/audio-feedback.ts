/**
 * Audio feedback utility for providing sound notifications
 * Used for success/error feedback in scanning and other operations
 */

export interface AudioOptions {
  volume?: number; // 0.0 to 1.0
  duration?: number; // in seconds
}

export interface SuccessAudioOptions extends AudioOptions {
  startFreq?: number; // Starting frequency in Hz
  endFreq?: number; // Ending frequency in Hz
}

export interface ErrorAudioOptions extends AudioOptions {
  startFreq?: number; // Starting frequency in Hz
  endFreq?: number; // Ending frequency in Hz
}

/**
 * Play a success sound - high pitch rising tone
 * @param options - Audio configuration options
 */
export const playSuccessSound = (options: SuccessAudioOptions = {}) => {
  const {
    volume = 0.3,
    duration = 0.3,
    startFreq = 800,
    endFreq = 1000,
  } = options;

  try {
    const context = new AudioContext();
    const oscillator = context.createOscillator();
    const gain = context.createGain();

    oscillator.connect(gain);
    gain.connect(context.destination);

    // Rising frequency for success (pleasant sound)
    oscillator.frequency.setValueAtTime(startFreq, context.currentTime);
    oscillator.frequency.setValueAtTime(endFreq, context.currentTime + 0.1);

    // Volume envelope
    gain.gain.setValueAtTime(0, context.currentTime);
    gain.gain.linearRampToValueAtTime(volume, context.currentTime + 0.01);
    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);

    // Clean up context after sound finishes
    setTimeout(() => {
      context.close();
    }, (duration + 0.1) * 1000);
  } catch (error) {
    console.log('Audio not supported or failed:', error);
  }
};

/**
 * Play an error sound - low pitch falling tone
 * @param options - Audio configuration options
 */
export const playErrorSound = (options: ErrorAudioOptions = {}) => {
  const {
    volume = 0.3,
    duration = 0.5,
    startFreq = 300,
    endFreq = 250,
  } = options;

  try {
    const context = new AudioContext();
    const oscillator = context.createOscillator();
    const gain = context.createGain();

    oscillator.connect(gain);
    gain.connect(context.destination);

    // Falling frequency for error (distinctive buzz)
    oscillator.frequency.setValueAtTime(startFreq, context.currentTime);
    oscillator.frequency.setValueAtTime(endFreq, context.currentTime + 0.1);

    // Volume envelope
    gain.gain.setValueAtTime(0, context.currentTime);
    gain.gain.linearRampToValueAtTime(volume, context.currentTime + 0.01);
    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);

    // Clean up context after sound finishes
    setTimeout(() => {
      context.close();
    }, (duration + 0.1) * 1000);
  } catch (error) {
    console.log('Audio not supported or failed:', error);
  }
};

/**
 * Play a warning sound - medium pitch oscillating tone
 * @param options - Audio configuration options
 */
export const playWarningSound = (options: AudioOptions = {}) => {
  const {
    volume = 0.3,
    duration = 0.4,
  } = options;

  try {
    const context = new AudioContext();
    const oscillator = context.createOscillator();
    const gain = context.createGain();

    oscillator.connect(gain);
    gain.connect(context.destination);

    // Oscillating frequency for warning
    oscillator.frequency.setValueAtTime(500, context.currentTime);
    oscillator.frequency.setValueAtTime(600, context.currentTime + 0.1);
    oscillator.frequency.setValueAtTime(500, context.currentTime + 0.2);
    oscillator.frequency.setValueAtTime(600, context.currentTime + 0.3);

    // Volume envelope
    gain.gain.setValueAtTime(0, context.currentTime);
    gain.gain.linearRampToValueAtTime(volume, context.currentTime + 0.01);
    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);

    // Clean up context after sound finishes
    setTimeout(() => {
      context.close();
    }, (duration + 0.1) * 1000);
  } catch (error) {
    console.log('Audio not supported or failed:', error);
  }
};

/**
 * Play a notification sound - single tone beep
 * @param options - Audio configuration options
 */
export const playNotificationSound = (options: AudioOptions & { frequency?: number } = {}) => {
  const {
    volume = 0.3,
    duration = 0.2,
    frequency = 440, // A4 note
  } = options;

  try {
    const context = new AudioContext();
    const oscillator = context.createOscillator();
    const gain = context.createGain();

    oscillator.connect(gain);
    gain.connect(context.destination);

    // Single frequency tone
    oscillator.frequency.setValueAtTime(frequency, context.currentTime);

    // Volume envelope
    gain.gain.setValueAtTime(0, context.currentTime);
    gain.gain.linearRampToValueAtTime(volume, context.currentTime + 0.01);
    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);

    // Clean up context after sound finishes
    setTimeout(() => {
      context.close();
    }, (duration + 0.1) * 1000);
  } catch (error) {
    console.log('Audio not supported or failed:', error);
  }
};

/**
 * Check if Web Audio API is supported
 * @returns boolean indicating audio support
 */
export const isAudioSupported = (): boolean => {
  try {
    return !!(window.AudioContext || (window as any).webkitAudioContext);
  } catch (error) {
    return false;
  }
};

/**
 * Audio feedback presets for common use cases
 */
export const AudioPresets = {
  // Scanning feedback
  scanSuccess: () => playSuccessSound({ startFreq: 800, endFreq: 1000, duration: 0.3 }),
  scanError: () => playErrorSound({ startFreq: 300, endFreq: 250, duration: 0.5 }),
  
  // Form feedback
  formSuccess: () => playSuccessSound({ startFreq: 600, endFreq: 800, duration: 0.25 }),
  formError: () => playErrorSound({ startFreq: 400, endFreq: 300, duration: 0.4 }),
  
  // General notifications
  notification: () => playNotificationSound({ frequency: 440, duration: 0.2 }),
  warning: () => playWarningSound({ duration: 0.4 }),
  
  // Button feedback
  buttonClick: () => playNotificationSound({ frequency: 800, duration: 0.1, volume: 0.2 }),
  buttonSuccess: () => playSuccessSound({ startFreq: 700, endFreq: 900, duration: 0.2, volume: 0.25 }),
  
  // Process feedback
  processComplete: () => playSuccessSound({ startFreq: 500, endFreq: 800, duration: 0.4 }),
  processError: () => playErrorSound({ startFreq: 350, endFreq: 200, duration: 0.6 }),
} as const;

/**
 * Audio feedback manager for controlling global audio settings
 */
export class AudioFeedbackManager {
  private static instance: AudioFeedbackManager;
  private enabled: boolean = true;
  private globalVolume: number = 1.0;

  private constructor() {}

  static getInstance(): AudioFeedbackManager {
    if (!AudioFeedbackManager.instance) {
      AudioFeedbackManager.instance = new AudioFeedbackManager();
    }
    return AudioFeedbackManager.instance;
  }

  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  isEnabled(): boolean {
    return this.enabled;
  }

  setGlobalVolume(volume: number): void {
    this.globalVolume = Math.max(0, Math.min(1, volume));
  }

  getGlobalVolume(): number {
    return this.globalVolume;
  }

  playSuccess(options: SuccessAudioOptions = {}): void {
    if (!this.enabled) return;
    playSuccessSound({
      ...options,
      volume: (options.volume || 0.3) * this.globalVolume,
    });
  }

  playError(options: ErrorAudioOptions = {}): void {
    if (!this.enabled) return;
    playErrorSound({
      ...options,
      volume: (options.volume || 0.3) * this.globalVolume,
    });
  }

  playWarning(options: AudioOptions = {}): void {
    if (!this.enabled) return;
    playWarningSound({
      ...options,
      volume: (options.volume || 0.3) * this.globalVolume,
    });
  }

  playNotification(options: AudioOptions & { frequency?: number } = {}): void {
    if (!this.enabled) return;
    playNotificationSound({
      ...options,
      volume: (options.volume || 0.3) * this.globalVolume,
    });
  }
}

// Export singleton instance for easy access
export const audioManager = AudioFeedbackManager.getInstance();

// Global types used across the application

// Pagination information returned by GraphQL queries
export interface PaginatorInfo {
  count: number;
  currentPage: number;
  firstItem: number;
  hasMorePages: boolean;
  lastItem: number;
  lastPage: number;
  perPage: number;
  total: number;
}

// Generic paginated response structure
export interface PaginatedResponse<T> {
  data: T[];
  paginatorInfo: PaginatorInfo;
}

// Sort order enum for ordering queries
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

// Base order by clause interface
export interface OrderByClause<T = string> {
  column: T;
  order: SortOrder;
}

// Common status types
export const COMMON_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  DRAFT: 'draft',
} as const;

export type CommonStatusType = typeof COMMON_STATUS[keyof typeof COMMON_STATUS];

// Base query variables for paginated queries
export interface BaseQueryVariables {
  first?: number;
  page?: number;
  search?: string;
  orderBy?: OrderByClause[];
}

// API response wrapper
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

// Error response structure
export interface ErrorResponse {
  message: string;
  errors?: Record<string, string[]>;
  code?: string;
}

// File upload types
export interface FileUpload {
  id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  alt_text?: string;
  created_at: string;
  updated_at: string;
}

// Address structure
export interface Address {
  id?: string;
  line_1: string;
  line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default?: boolean;
}

// Contact information
export interface ContactInfo {
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
}

// Audit fields for entities
export interface AuditFields {
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

// Base entity interface
export interface BaseEntity extends AuditFields {
  id: string;
  is_active: boolean;
}

// Money/Currency types
export interface Money {
  amount: number;
  currency: string;
}

// Date range filter
export interface DateRange {
  start_date?: string;
  end_date?: string;
}

// Numeric range filter
export interface NumericRange {
  min?: number;
  max?: number;
}

// Search and filter base interface
export interface SearchFilters extends BaseQueryVariables {
  date_range?: DateRange;
  status?: string;
  is_active?: boolean;
}

// Generic ID type
export type ID = string | number;

// Generic key-value pair
export interface KeyValue<T = any> {
  key: string;
  value: T;
  label?: string;
}

// Option type for dropdowns/selects
export interface Option<T = any> {
  value: T;
  label: string;
  disabled?: boolean;
  group?: string;
}

// Coordinates for location-based features
export interface Coordinates {
  latitude: number;
  longitude: number;
}

// Location with address and coordinates
export interface Location extends Address {
  coordinates?: Coordinates;
  place_id?: string;
  formatted_address?: string;
}

// Notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  created_at: string;
  action_url?: string;
}

// User session information
export interface UserSession {
  user_id: string;
  session_id: string;
  expires_at: string;
  last_activity: string;
  ip_address?: string;
  user_agent?: string;
}

// Feature flag type
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  description?: string;
  conditions?: Record<string, any>;
}

// Configuration setting
export interface ConfigSetting {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description?: string;
  is_public: boolean;
}

// Bulk operation result
export interface BulkOperationResult {
  total: number;
  successful: number;
  failed: number;
  errors: string[];
  processed_ids: string[];
}

// Export/Import job status
export interface JobStatus {
  id: string;
  type: 'export' | 'import' | 'bulk_operation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  total_records?: number;
  processed_records?: number;
  file_url?: string;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

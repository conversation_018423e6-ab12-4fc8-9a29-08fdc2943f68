import { useQuery, UseQueryOptions } from 'react-query';

import { GET_ORDERS, GET_ORDER, GET_ORDER_STATUSES } from './orders.query';
import {
  OrdersResponse,
  OrderResponse,
  OrderStatusesResponse,
  OrdersQueryVariables,
  QueryOrdersOrderByOrderByClause,
} from './orders.types';
import { OrderColumn } from '../graphql-columns';
import { executeGraphQLQuery } from '@utils/graphql-executor';

// Fetch orders with pagination and filtering
export const fetchOrders = async (
  variables: OrdersQueryVariables = {}
): Promise<OrdersResponse> => {
  return executeGraphQLQuery<OrdersResponse>(GET_ORDERS, variables);
};

// Fetch single order by ID
export const fetchOrder = async (id: string): Promise<OrderResponse> => {
  return executeGraphQLQuery<OrderResponse>(GET_ORDER, { id });
};

// Fetch order statuses
export const fetchOrderStatuses = async (): Promise<OrderStatusesResponse> => {
  return executeGraphQLQuery<OrderStatusesResponse>(GET_ORDER_STATUSES);
};

// React Query hook for orders list
export const useOrdersQuery = (
  variables?: OrdersQueryVariables,
  options?: UseQueryOptions<OrdersResponse, Error>
) => {
  // Provide default values for empty calls
  const queryVariables = variables || {};

  return useQuery<OrdersResponse, Error>(
    ['orders', queryVariables],
    () => fetchOrders(queryVariables),
    {
      keepPreviousData: true,
      staleTime: 30 * 1000, // 30 seconds
      ...options,
    }
  );
};

// React Query hook for single order
export const useOrderQuery = (
  id: string,
  options?: UseQueryOptions<OrderResponse, Error>
) => {
  return useQuery<OrderResponse, Error>(['order', id], () => fetchOrder(id), {
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minute
    ...options,
  });
};

// React Query hook for order statuses
export const useOrderStatusesQuery = (
  options?: UseQueryOptions<OrderStatusesResponse, Error>
) => {
  return useQuery<OrderStatusesResponse, Error>(
    ['orderStatuses'],
    fetchOrderStatuses,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

// Helper function to get status color
export const getOrderStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'pending':
    case 'pending_payment':
      return 'bg-yellow-100 text-yellow-800';
    case 'processing':
    case 'packed':
      return 'bg-blue-100 text-blue-800';
    case 'delivery':
    case 'out_for_delivery':
      return 'bg-purple-100 text-purple-800';
    case 'complete':
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'canceled':
    case 'cancelled':
    case 'failed':
      return 'bg-red-100 text-red-800';
    case 'holded':
    case 'payment_review':
      return 'bg-orange-100 text-orange-800';
    case 'refunded':
    case 'in_refund_process_b':
    case 'in_refund_process_sc':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper function to format order status display name
export const formatOrderStatus = (status: string): string => {
  return status
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper function for building order by clauses
export const buildOrderBy = (
  sortBy?: string,
  sortOrder?: 'ASC' | 'DESC'
): QueryOrdersOrderByOrderByClause[] => {
  if (!sortBy) {
    return [{ column: OrderColumn.UPDATED_AT, order: 'DESC' }];
  }

  // Map common sort fields to OrderColumn enum values
  const columnMap: Record<string, string> = {
    id: OrderColumn.ID,
    display_id: OrderColumn.DISPLAY_ID,
    external_order_id: OrderColumn.EXTERNAL_ORDER_ID,
    tracking_number: OrderColumn.TRACKING_NUMBER,
    type: OrderColumn.TYPE,
    customer_id: OrderColumn.CUSTOMER_ID,
    sale_by_id: OrderColumn.SALE_BY_ID,
    customer_name: OrderColumn.CUSTOMER_NAME,
    customer_contact: OrderColumn.CUSTOMER_CONTACT,
    customer_email: OrderColumn.CUSTOMER_EMAIL,
    status: OrderColumn.STATUS,
    amount: OrderColumn.AMOUNT,
    sales_tax: OrderColumn.SALES_TAX,
    paid_total: OrderColumn.PAID_TOTAL,
    total: OrderColumn.TOTAL,
    total_invoiced: OrderColumn.TOTAL_INVOICED,
    total_refunded: OrderColumn.TOTAL_REFUNDED,
    payment_gateway: OrderColumn.PAYMENT_GATEWAY,
    delivery_fee: OrderColumn.DELIVERY_FEE,
    delivery_time: OrderColumn.DELIVERY_TIME,
    created_at: OrderColumn.CREATED_AT,
    updated_at: OrderColumn.UPDATED_AT,
  };

  const column = columnMap[sortBy.toLowerCase()] || sortBy.toUpperCase();

  return [
    {
      column: column,
      order: sortOrder || 'DESC',
    },
  ];
};

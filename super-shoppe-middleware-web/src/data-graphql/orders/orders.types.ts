import { PaginatorInfo } from '../../types';

export interface Order {
  id: string;
  display_id?: string;
  external_order_id?: string;
  tracking_number?: string;
  type?: string;
  require_shipping: boolean;
  customer_id?: string;
  sale_by_id?: string;
  customer_name?: string;
  customer_contact?: string;
  customer_email?: string;
  amount: number;
  sales_tax?: number;
  paid_total: number;
  total: number;
  total_invoiced?: number;
  total_refunded?: number;
  coupon_id?: string;
  parent_id?: string;
  shop_id?: string;
  coupon_discount?: number;
  discount?: number;
  points_earn?: number;
  points_used?: number;
  points_discount?: number;
  golds_earn?: number;
  golds_used?: number;
  golds_discount?: number;
  store_credit_earn?: number;
  store_credit?: number;
  gift_card_credit?: number;
  payment_method_id?: string;
  payment_method_info?: any;
  payment_id?: string;
  payment_gateway?: string;
  shipping_address?: any;
  billing_address?: any;
  state_id?: string;
  shipping_class_id?: string;
  items_weight?: number;
  delivery_fee?: number;
  delivery_time?: string;
  note?: string;
  created_at: string;
  updated_at: string;

  // Relationships
  customer?: User;
  sale_by?: User;
  shop?: Shop;
  parent?: Order;
  children?: Order[];
  order_products?: OrderProduct[];
  status?: OrderStatus;
}

export interface OrderProduct {
  id: string;
  order_id: string;
  product_id: string;
  inventory_id?: string;
  variation_option_id?: string;
  name: string;
  sku?: string;
  barcode?: string;
  width?: number;
  height?: number;
  length?: number;
  weight?: number;
  image?: string;
  banner?: string;
  order_quantity: number;
  invoiced_quantity?: number;
  shipped_quantity?: number;
  canceled_quantity?: number;
  refunded_quantity?: number;
  unit_price: number;
  upfront_amount?: number;
  discount_rate_type?: string;
  discount_rate?: number;
  discount?: number;
  store_credit?: number;
  gift_card_credit?: number;
  subtotal: number;
  tax?: number;
  is_commissioned?: boolean;
  is_deposit?: boolean;
  created_at: string;
  updated_at: string;

  // Relationships
  order?: Order;
  product?: Product;
}

export interface OrderStatus {
  id: string;
  name: string;
  slug: string;
  serial: number;
  color?: string;
  default: boolean;
  created_at: string;
  updated_at: string;
}

// Import Customer type from customer module
import { Customer } from '../customer/customer.types';

export interface User extends Customer {
  // User is an alias for Customer in the orders context
}

export interface Product {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  gallery?: string[];
}

export interface Shop {
  id: string;
  name: string;
  slug?: string;
}

export interface OrdersQueryVariables {
  first?: number;
  page?: number;
  where?: ComplexFilter;
  orderBy?: QueryOrdersOrderByOrderByClause[];
}

// Import types from query builder
import { ComplexFilter } from '../../utils/graphql-query-builder';

export interface QueryOrdersOrderByOrderByClause {
  column: string;
  order: 'ASC' | 'DESC';
}

// Keep the old interface for backward compatibility
export interface OrderByClause {
  column: string;
  order: 'ASC' | 'DESC';
}

export interface OrdersResponse {
  orders: {
    paginatorInfo: PaginatorInfo;
    data: Order[];
  };
}

export interface OrderResponse {
  order: Order;
}

export interface OrderStatusesResponse {
  orderStatuses: OrderStatus[];
}

// Order status constants based on the enum
export const ORDER_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  PACKED: 'packed',
  DELIVERY: 'delivery',
  COMPLETE: 'complete',
  HOLDED: 'holded',
  PAYMENT_REVIEW: 'payment_review',
  PENDING_PAYMENT: 'pending_payment',
  FRAUD: 'fraud',
  CANCELED: 'canceled',
  CLOSED: 'closed',
  IN_REFUND_PROCESS_B: 'in_refund_process_b',
  IN_REFUND_PROCESS_SC: 'in_refund_process_sc',
  ARRIVE_AT_DROPZONE: 'arrive_at_dropzone',
  FAILED_DELIVERY: 'failed_delivery',
} as const;

export type OrderStatusType = (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS];

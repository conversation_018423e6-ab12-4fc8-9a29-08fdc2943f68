import { gql } from 'graphql-request';

export const GET_ORDERS = gql`
  query GetOrders(
    $where: QueryOrdersWhereWhereConditions
    $orderBy: [QueryOrdersOrderByOrderByClause!]
    $first: Int
    $page: Int
  ) {
    orders(where: $where, orderBy: $orderBy, first: $first, page: $page) {
      paginatorInfo {
        count
        currentPage
        firstItem
        hasMorePages
        lastItem
        lastPage
        perPage
        total
      }
      data {
        id
        display_id
        tracking_number
        customer_name
        customer_contact
        customer_email
        amount
        sales_tax
        paid_total
        total
        payment_gateway
        delivery_fee
        created_at
        updated_at

        # Customer relationship
        customer {
          id
          name
          email
          phone
        }

        # Sale by relationship
        sale_by {
          id
          name
          email
        }

        # Order products
        order_products {
          id
          name
          sku
          barcode
          order_quantity
          unit_price
          subtotal
          product {
            id
            name
            image
          }
        }

        # Status info
        status {
          id
          name
          slug
          color
          serial
        }
      }
    }
  }
`;

export const GET_ORDER = gql`
  query GetOrder($id: ID!) {
    order(id: $id) {
      id
      display_id
      external_order_id
      tracking_number
      type
      require_shipping
      customer_name
      customer_contact
      customer_email
      amount
      sales_tax
      paid_total
      total
      total_invoiced
      total_refunded
      discount
      delivery_fee
      delivery_time
      payment_gateway
      payment_id
      shipping_address
      billing_address
      note
      created_at
      updated_at

      # Customer relationship
      customer {
        id
        name
        firstname
        lastname
        email
        phone
        is_active
      }

      # Sale by relationship
      sale_by {
        id
        name
        email
      }

      # Order products with full details
      order_products {
        id
        name
        sku
        barcode
        order_quantity
        invoiced_quantity
        shipped_quantity
        canceled_quantity
        refunded_quantity
        unit_price
        discount
        subtotal
        tax
        product {
          id
          name
          slug
          description
          image
          gallery
          barcode
        }
      }

      # Status info
      status {
        id
        name
        slug
        color
        serial
      }

      # Parent order if exists
      parent {
        id
        display_id
        status {
          id
          name
          slug
          color
          serial
        }
      }

      # Child orders if exists
      children {
        id
        display_id
        status {
          id
          name
          slug
          color
          serial
        }
        total
      }
    }
  }
`;

export const GET_ORDER_STATUSES = gql`
  query GetOrderStatuses {
    orderStatuses {
      id
      name
      slug
      serial
      color
      default
    }
  }
`;

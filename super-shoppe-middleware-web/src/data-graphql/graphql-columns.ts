/**
 * GraphQL Column Definitions
 *
 * This file contains all column enums used for GraphQL filtering and sorting.
 * Each enum corresponds to columns available for a specific GraphQL entity.
 */

// Product column enum
export enum ProductColumn {
  ID = 'ID',
  NAME = 'NAME',
  SLUG = 'SLUG',
  DESCRIPTION = 'DESCRIPTION',
  PRICE = 'PRICE',
  SALE_PRICE = 'SALE_PRICE',
  SKU = 'SKU',
  BARCODE = 'BARCODE',
  STATUS = 'STATUS',
  UNIT = 'UNIT',
  HEIGHT = 'HEIGHT',
  WIDTH = 'WIDTH',
  LENGTH = 'LENGTH',
  WEIGHT = 'WEIGHT',
  IS_TAXABLE = 'IS_TAXABLE',
  IS_BUNDLE = 'IS_BUNDLE',
  IS_REQUIRE_DOUBLE_SCANNING = 'IS_REQUIRE_DOUBLE_SCANNING',
  TAX_CLASS_ID = 'TAX_CLASS_ID',
  CREATED_AT = 'CREATED_AT',
  UPDATED_AT = 'UPDATED_AT',
}

export const PRODUCT_COLUMNS = [
  { value: ProductColumn.ID, label: 'Product ID' },
  { value: ProductColumn.NAME, label: 'Product Name' },
  { value: ProductColumn.SLUG, label: 'Slug' },
  { value: ProductColumn.DESCRIPTION, label: 'Description' },
  { value: ProductColumn.SKU, label: 'SKU' },
  { value: ProductColumn.BARCODE, label: 'Barcode' },
  { value: ProductColumn.PRICE, label: 'Price' },
  { value: ProductColumn.SALE_PRICE, label: 'Sale Price' },
  { value: ProductColumn.STATUS, label: 'Status' },
  { value: ProductColumn.UNIT, label: 'Unit' },
  { value: ProductColumn.HEIGHT, label: 'Height' },
  { value: ProductColumn.WIDTH, label: 'Width' },
  { value: ProductColumn.LENGTH, label: 'Length' },
  { value: ProductColumn.WEIGHT, label: 'Weight' },
  { value: ProductColumn.IS_TAXABLE, label: 'Is Taxable' },
  { value: ProductColumn.IS_BUNDLE, label: 'Is Bundle' },
  {
    value: ProductColumn.IS_REQUIRE_DOUBLE_SCANNING,
    label: 'Require Double Scanning',
  },
  { value: ProductColumn.TAX_CLASS_ID, label: 'Tax Class ID' },
  { value: ProductColumn.CREATED_AT, label: 'Created Date' },
  { value: ProductColumn.UPDATED_AT, label: 'Updated Date' },
];

// Order column enum
export enum OrderColumn {
  ID = 'ID',
  DISPLAY_ID = 'DISPLAY_ID',
  EXTERNAL_ORDER_ID = 'EXTERNAL_ORDER_ID',
  AUTH_REF_ID = 'AUTH_REF_ID',
  TRACKING_NUMBER = 'TRACKING_NUMBER',
  TYPE = 'TYPE',
  REQUIRE_SHIPPING = 'REQUIRE_SHIPPING',
  CUSTOMER_ID = 'CUSTOMER_ID',
  SALE_BY_ID = 'SALE_BY_ID',
  CUSTOMER_NAME = 'CUSTOMER_NAME',
  CUSTOMER_CONTACT = 'CUSTOMER_CONTACT',
  CUSTOMER_EMAIL = 'CUSTOMER_EMAIL',
  STATUS = 'STATUS',
  AMOUNT = 'AMOUNT',
  SALES_TAX = 'SALES_TAX',
  PAID_TOTAL = 'PAID_TOTAL',
  TOTAL = 'TOTAL',
  TOTAL_INVOICED = 'TOTAL_INVOICED',
  TOTAL_REFUNDED = 'TOTAL_REFUNDED',
  COUPON_ID = 'COUPON_ID',
  PARENT_ID = 'PARENT_ID',
  SHOP_ID = 'SHOP_ID',
  COUPON_DISCOUNT = 'COUPON_DISCOUNT',
  DISCOUNT = 'DISCOUNT',
  POINTS_EARN = 'POINTS_EARN',
  POINTS_USED = 'POINTS_USED',
  POINTS_DISCOUNT = 'POINTS_DISCOUNT',
  GOLDS_EARN = 'GOLDS_EARN',
  GOLDS_USED = 'GOLDS_USED',
  GOLDS_DISCOUNT = 'GOLDS_DISCOUNT',
  STORE_CREDIT_EARN = 'STORE_CREDIT_EARN',
  STORE_CREDIT = 'STORE_CREDIT',
  GIFT_CARD_CREDIT = 'GIFT_CARD_CREDIT',
  PAYMENT_METHOD_ID = 'PAYMENT_METHOD_ID',
  PAYMENT_ID = 'PAYMENT_ID',
  PAYMENT_GATEWAY = 'PAYMENT_GATEWAY',
  STATE_ID = 'STATE_ID',
  SHIPPING_CLASS_ID = 'SHIPPING_CLASS_ID',
  ITEMS_WEIGHT = 'ITEMS_WEIGHT',
  DELIVERY_FEE = 'DELIVERY_FEE',
  DELIVERY_TIME = 'DELIVERY_TIME',
  NOTE = 'NOTE',
  CREATED_AT = 'CREATED_AT',
  UPDATED_AT = 'UPDATED_AT',
}

export const ORDER_COLUMNS = [
  { value: OrderColumn.ID, label: 'Order ID' },
  { value: OrderColumn.DISPLAY_ID, label: 'Display ID' },
  { value: OrderColumn.EXTERNAL_ORDER_ID, label: 'External Order ID' },
  { value: OrderColumn.TRACKING_NUMBER, label: 'Tracking Number' },
  { value: OrderColumn.TYPE, label: 'Order Type' },
  { value: OrderColumn.CUSTOMER_ID, label: 'Customer ID' },
  { value: OrderColumn.SALE_BY_ID, label: 'Sale By ID' },
  { value: OrderColumn.CUSTOMER_NAME, label: 'Customer Name' },
  { value: OrderColumn.CUSTOMER_CONTACT, label: 'Customer Contact' },
  { value: OrderColumn.CUSTOMER_EMAIL, label: 'Customer Email' },
  { value: OrderColumn.STATUS, label: 'Status' },
  { value: OrderColumn.AMOUNT, label: 'Amount' },
  { value: OrderColumn.SALES_TAX, label: 'Sales Tax' },
  { value: OrderColumn.PAID_TOTAL, label: 'Paid Total' },
  { value: OrderColumn.TOTAL, label: 'Total' },
  { value: OrderColumn.TOTAL_INVOICED, label: 'Total Invoiced' },
  { value: OrderColumn.TOTAL_REFUNDED, label: 'Total Refunded' },
  { value: OrderColumn.PAYMENT_GATEWAY, label: 'Payment Gateway' },
  { value: OrderColumn.DELIVERY_FEE, label: 'Delivery Fee' },
  { value: OrderColumn.DELIVERY_TIME, label: 'Delivery Time' },
  { value: OrderColumn.NOTE, label: 'Note' },
  { value: OrderColumn.CREATED_AT, label: 'Created Date' },
  { value: OrderColumn.UPDATED_AT, label: 'Updated Date' },
];

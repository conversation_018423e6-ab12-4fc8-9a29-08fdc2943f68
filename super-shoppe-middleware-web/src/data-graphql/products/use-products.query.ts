import { useQuery, UseQueryOptions } from 'react-query';
import { GET_PRODUCTS, GET_PRODUCT } from './products.query';
import {
  ProductsResponse,
  ProductResponse,
  ProductsQueryVariables,
} from './products.types';
import { executeGraphQLQuery } from '@utils/graphql-executor';

// Fetch products with pagination and filtering
export const fetchProducts = async (
  variables: ProductsQueryVariables = {}
): Promise<ProductsResponse> => {
  return executeGraphQLQuery<ProductsResponse>(GET_PRODUCTS, variables);
};

// Fetch single product by ID
export const fetchProduct = async (id: string): Promise<ProductResponse> => {
  return executeGraphQLQuery<ProductResponse>(GET_PRODUCT, { id });
};

// React Query hook for products list
export const useProductsQuery = (
  variables?: ProductsQueryVariables,
  options?: UseQueryOptions<ProductsResponse, Error>
) => {
  const queryVariables = variables || {};

  return useQuery<ProductsResponse, Error>(
    ['products', queryVariables],
    () => fetchProducts(queryVariables),
    {
      enabled: false, // Don't auto-fetch, only fetch when needed
      staleTime: 30 * 1000, // 30 seconds
      ...options,
    }
  );
};

// React Query hook for single product
export const useProductQuery = (
  id: string,
  options?: UseQueryOptions<ProductResponse, Error>
) => {
  return useQuery<ProductResponse, Error>(
    ['product', id],
    () => fetchProduct(id),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
};

// Product types and interfaces
import { PaginatorInfo } from '../../types/global';
export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price?: number;
  sale_price?: number;
  sku?: string;
  barcode?: string;
  is_taxable?: boolean;
  tax_class_id?: string;
  shipping_class_id?: string;
  variant_attributes?: any; // JSON
  meta?: any; // JSON
  status?: string;
  unit?: string;
  height?: number;
  width?: number;
  length?: number;
  weight?: number;
  is_bundle?: boolean;
  is_require_double_scanning?: boolean;
  image?: string; // Media URL
  gallery?: string[]; // Media URLs
  created_at: string;
  updated_at: string;

  // Relationships
  variants?: ProductVariant[];
  categories?: Category[];
  bundle_items?: ProductBundleItem[];
  productable?: Productable;
}

export interface ProductVariant {
  id: string;
  product_id: string;
  title: string;
  price?: number;
  sale_price?: number;
  sku?: string;
  barcode?: string;
  is_active?: boolean;
  width?: number;
  height?: number;
  length?: number;
  weight?: number;
  options?: any; // JSON
  gallery?: string[]; // Media URLs
  created_at: string;
  updated_at: string;
}

// Category interface (from Category model)
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  meta?: any; // JSON
  created_at: string;
  updated_at: string;
}

// Product Bundle Item interface
export interface ProductBundleItem {
  id: string;
  bundle_product_id: string;
  item_product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;

  // Relationships
  bundle?: Product;
  item?: Product;
}

// Productable union type (polymorphic relationship)
export type Productable = any; // This would be ClassType or other productable models

export interface ProductMedia {
  id: string;
  product_id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  alt_text?: string;
  sort_order: number;
  is_primary: boolean;
}

// Query variables and responses
export interface ProductsQueryVariables {
  first?: number;
  page?: number;
  where?: any; // WhereConditions for filtering
  orderBy?: any; // OrderBy conditions
}

export interface ProductsResponse {
  products: {
    data: Product[];
    paginatorInfo: PaginatorInfo;
  };
}

export interface ProductResponse {
  product: Product;
}

export interface CategoriesResponse {
  categories: {
    data: Category[];
    paginatorInfo: PaginatorInfo;
  };
}

// Product status types
export const PRODUCT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  OUT_OF_STOCK: 'out_of_stock',
  LOW_STOCK: 'low_stock',
} as const;

export type ProductStatusType =
  (typeof PRODUCT_STATUS)[keyof typeof PRODUCT_STATUS];

import { gql } from 'graphql-request';

// Get products with pagination and filtering
export const GET_PRODUCTS = gql`
  query GetProducts(
    $first: Int
    $page: Int
    $where: QueryProductsWhereWhereConditions
    $orderBy: [QueryProductsOrderByOrderByClause!]
  ) {
    products(first: $first, page: $page, where: $where, orderBy: $orderBy) {
      data {
        id
        name
        slug
        description
        price
        sale_price
        sku
        barcode
        is_taxable
        tax_class_id
        shipping_class_id
        variant_attributes
        meta
        status
        unit
        height
        width
        length
        weight
        is_bundle
        is_require_double_scanning
        image
        gallery
        created_at
        updated_at
        categories {
          id
          name
          slug
          meta
          created_at
          updated_at
        }
        variants {
          id
          product_id
          title
          price
          sale_price
          sku
          barcode
          is_active
          width
          height
          length
          weight
          options
          gallery
          created_at
          updated_at
        }
      }
      paginatorInfo {
        count
        currentPage
        firstItem
        hasMorePages
        lastItem
        lastPage
        perPage
        total
      }
    }
  }
`;

// Get single product by ID
export const GET_PRODUCT = gql`
  query GetProduct($id: ID!) {
    product(id: $id) {
      id
      name
      slug
      description
      price
      sale_price
      sku
      barcode
      is_taxable
      tax_class_id
      shipping_class_id
      variant_attributes
      meta
      status
      unit
      height
      width
      length
      weight
      is_bundle
      is_require_double_scanning
      image
      gallery
      created_at
      updated_at
      categories {
        id
        name
        slug
        description
        meta
        created_at
        updated_at
      }
      variants {
        id
        product_id
        title
        price
        sale_price
        sku
        barcode
        is_active
        width
        height
        length
        weight
        options
        gallery
        created_at
        updated_at
      }
    }
  }
`;

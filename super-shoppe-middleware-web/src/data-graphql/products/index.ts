// Export all product-related functionality
export * from './products.query';
export * from './products.types';
export * from './use-products.query';

// Re-export commonly used items for convenience
export {
  fetchProducts,
  fetchProduct,
  useProductsQuery,
  useProductQuery,
} from './use-products.query';

export {
  PRODUCT_STATUS,
  type Product,
  type Category,
  type ProductVariant,
  type ProductBundleItem,
  type ProductMedia,
  type ProductsQueryVariables,
  type ProductStatusType,
  type Productable,
} from './products.types';

export { GET_PRODUCTS, GET_PRODUCT } from './products.query';

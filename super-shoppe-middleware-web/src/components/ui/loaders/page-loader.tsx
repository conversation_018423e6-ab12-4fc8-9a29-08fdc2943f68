import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';

interface PageLoaderProps {
  text?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  overlay?: boolean;
  showText?: boolean;
}

const PageLoader: React.FC<PageLoaderProps> = ({
  text,
  className,
  size = 'md',
  overlay = false,
  showText = true,
}) => {
  const { t } = useTranslation();

  const sizeClasses = {
    sm: 'w-8 h-8 border-2',
    md: 'w-12 h-12 border-4',
    lg: 'w-16 h-16 border-4',
  };

  const containerClasses = cn(
    'flex flex-col items-center justify-center',
    overlay ? 'fixed inset-0 z-50 bg-black bg-opacity-25' : 'w-full h-screen',
    className
  );

  const spinnerClasses = cn(
    'animate-spin rounded-full border-gray-200 border-t-blue-600',
    sizeClasses[size]
  );

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center justify-center bg-white rounded-lg p-8 shadow-lg">
        <div className={spinnerClasses}></div>
        {showText && (
          <p className="mt-4 text-gray-600 text-sm font-medium">
            {text || t('common:text-loading') || 'Loading...'}
          </p>
        )}
      </div>
    </div>
  );
};

export default PageLoader;

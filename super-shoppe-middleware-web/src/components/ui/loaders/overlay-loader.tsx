import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';
import Spinner from './spinner';

interface OverlayLoaderProps {
  text?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const OverlayLoader: React.FC<OverlayLoaderProps> = ({
  text,
  className,
  size = 'lg',
}) => {
  const { t } = useTranslation();

  const containerClasses = cn(
    'fixed inset-0 flex flex-col items-center justify-center z-50 bg-black bg-opacity-25',
    className
  );

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center justify-center bg-white rounded-lg p-8 shadow-lg">
        <Spinner size={size} color="primary" />
        <p className="mt-4 text-gray-600 text-sm font-medium">
          {text || t('common:text-loading') || 'Loading...'}
        </p>
      </div>
    </div>
  );
};

export default OverlayLoader;

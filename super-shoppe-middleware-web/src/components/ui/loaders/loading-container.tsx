import cn from 'classnames';
import Spinner from './spinner';
import { useTranslation } from 'next-export-i18n';

interface LoadingContainerProps {
  loading: boolean;
  children: React.ReactNode;
  text?: string;
  className?: string;
  spinnerSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  spinnerColor?: 'primary' | 'secondary' | 'white' | 'gray';
  overlay?: boolean;
  minHeight?: string;
}

const LoadingContainer: React.FC<LoadingContainerProps> = ({
  loading,
  children,
  text,
  className,
  spinnerSize = 'md',
  spinnerColor = 'primary',
  overlay = false,
  minHeight = '200px',
}) => {
  const { t } = useTranslation();

  if (!loading) {
    return <>{children}</>;
  }

  const containerClasses = cn(
    'flex flex-col items-center justify-center',
    overlay ? 'absolute inset-0 bg-white bg-opacity-75 z-10' : 'w-full',
    className
  );

  return (
    <div className="relative">
      {overlay && children}
      <div 
        className={containerClasses}
        style={{ minHeight: overlay ? undefined : minHeight }}
      >
        <Spinner size={spinnerSize} color={spinnerColor} />
        {text && (
          <p className="mt-3 text-gray-600 text-sm font-medium">
            {text}
          </p>
        )}
        {!text && (
          <p className="mt-3 text-gray-600 text-sm font-medium">
            {t('common:text-loading') || 'Loading...'}
          </p>
        )}
      </div>
      {!overlay && loading && (
        <div className="sr-only">
          {text || t('common:text-loading') || 'Loading...'}
        </div>
      )}
    </div>
  );
};

export default LoadingContainer;

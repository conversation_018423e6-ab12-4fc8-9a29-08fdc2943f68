# Loader Components

This directory contains standardized loading components for the application.

## Components

### 1. PageLoader
Full-page loading component for initial page loads or authentication checks.

```tsx
import { PageLoader } from '@components/ui/loaders';

// Basic usage
<PageLoader />

// With custom text and size
<PageLoader text="Authenticating..." size="lg" />

// As overlay
<PageLoader overlay text="Processing..." />
```

**Props:**
- `text?: string` - Custom loading text
- `className?: string` - Additional CSS classes
- `size?: 'sm' | 'md' | 'lg'` - Spinner size
- `overlay?: boolean` - Show as overlay
- `showText?: boolean` - Show/hide text (default: true)

### 2. Spinner
Standalone spinner component for inline loading states.

```tsx
import { Spinner } from '@components/ui/loaders';

// Basic usage
<Spinner />

// Different sizes and colors
<Spinner size="lg" color="primary" />
<Spinner size="sm" color="white" />
```

**Props:**
- `size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'` - Spinner size
- `color?: 'primary' | 'secondary' | 'white' | 'gray'` - Color theme
- `className?: string` - Additional CSS classes

### 3. LoadingContainer
Wrapper component that shows loading state over content.

```tsx
import { LoadingContainer } from '@components/ui/loaders';

<LoadingContainer loading={isLoading} text="Loading data...">
  <YourContent />
</LoadingContainer>

// As overlay
<LoadingContainer loading={isLoading} overlay>
  <YourContent />
</LoadingContainer>
```

**Props:**
- `loading: boolean` - Whether to show loading state
- `children: React.ReactNode` - Content to wrap
- `text?: string` - Loading text
- `className?: string` - Additional CSS classes
- `spinnerSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'` - Spinner size
- `spinnerColor?: 'primary' | 'secondary' | 'white' | 'gray'` - Spinner color
- `overlay?: boolean` - Show as overlay over content
- `minHeight?: string` - Minimum height when not overlay

### 4. OverlayLoader
Fixed overlay loader for blocking interactions.

```tsx
import { OverlayLoader } from '@components/ui/loaders';

{isProcessing && <OverlayLoader text="Processing payment..." />}
```

**Props:**
- `text?: string` - Loading text
- `className?: string` - Additional CSS classes
- `size?: 'sm' | 'md' | 'lg'` - Spinner size

## Content Loaders

For skeleton loading states, use the existing content loaders:

- `ProductCardLoader` - Product card skeleton
- `CategoryListCardLoader` - Category list skeleton
- `CategoryListingLoader` - Category listing skeleton
- `SearchResultLoader` - Search result skeleton
- `StoreListLoader` - Store list skeleton
- `TextLoader` - Text skeleton

## Usage Guidelines

1. **PageLoader**: Use for full-page loading states (authentication, initial page load)
2. **Spinner**: Use for inline loading indicators (buttons, small sections)
3. **LoadingContainer**: Use when you need to show loading over existing content
4. **OverlayLoader**: Use for blocking operations (payments, form submissions)
5. **Content Loaders**: Use for skeleton loading states while content is loading

## Accessibility

All loaders include:
- Proper ARIA labels
- Screen reader announcements
- Semantic HTML structure
- Focus management considerations

import React from 'react';
import { useRouter } from 'next/router';
import cn from 'classnames';

interface MobileHeaderProps {
  title: string;
  onBack?: () => void;
  showBack?: boolean;
  rightComponent?: React.ReactNode;
  className?: string;
  titleClassName?: string;
  backButtonClassName?: string;
  variant?: 'default' | 'transparent' | 'primary';
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  onBack,
  showBack = true,
  rightComponent,
  className,
  titleClassName,
  backButtonClassName,
  variant = 'default',
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  const variantClasses = {
    default: 'bg-white shadow-sm border-b border-gray-100',
    transparent: 'bg-transparent',
    primary: 'bg-blue-600 text-white shadow-md',
  };

  const titleVariantClasses = {
    default: 'text-gray-900',
    transparent: 'text-gray-900',
    primary: 'text-white',
  };

  const backButtonVariantClasses = {
    default: 'text-gray-600 hover:text-gray-800',
    transparent: 'text-gray-600 hover:text-gray-800',
    primary: 'text-white hover:text-gray-200',
  };

  return (
    <div
      className={cn(
        'sticky top-0 z-40 px-4 py-4',
        variantClasses[variant],
        className
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {showBack && (
            <button
              onClick={handleBack}
              className={cn(
                'flex-shrink-0 p-1 rounded-lg transition-colors duration-200',
                backButtonVariantClasses[variant],
                backButtonClassName
              )}
              aria-label="Go back"
            >
              <svg 
                className="w-6 h-6" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M15 19l-7-7 7-7" 
                />
              </svg>
            </button>
          )}
          <h1
            className={cn(
              'text-xl font-semibold truncate',
              titleVariantClasses[variant],
              titleClassName
            )}
          >
            {title}
          </h1>
        </div>
        
        {rightComponent && (
          <div className="flex-shrink-0 ml-3">
            {rightComponent}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileHeader;

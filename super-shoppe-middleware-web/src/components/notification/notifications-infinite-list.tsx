import NotificationItem from '@components/notification/notification-item';
import ProductCardGridLoader from '@components/ui/loaders/product-card-grid-loader';
import StoreListLoader from '@components/ui/loaders/store-list-loader';
import Text from '@components/ui/text';
import { useNotificationsQuery } from '@framework/notification/use-notifications-query';
import { SORT_TYPE } from '@framework/types';
import { Spinner } from '@components/ui/loaders';
import { Waypoint } from 'react-waypoint';

interface NotificationsInfiniteListProps {}

const NotificationsInfiniteList: React.FC<
  NotificationsInfiniteListProps
> = () => {
  const {
    isFetching: isLoading,
    isFetchingNextPage: loadingMore,
    fetchNextPage,
    hasNextPage,
    data,
  } = useNotificationsQuery({
    limit: 10,
    order_by: 'created_at',
    sorted_by: SORT_TYPE.DESC,
  });

  if (!data) {
    return (
      <>
        {Array.from({ length: 6 }).map((_, idx) => (
          <StoreListLoader
            key={`product--key-${idx}`}
            uniqueKey={`product--key-${idx}`}
          />
        ))}
      </>
    );
  }

  function loadMoreItems() {
    if (hasNextPage) {
      fetchNextPage();
    }
  }

  return (
    <div>
      {!data.pages.length || !data.pages[0].data.length ? (
        <div className="border bg-skin-secondary px-4 py-2">
          <Text variant="medium">No notification found</Text>
        </div>
      ) : (
        <div className="bg-white mb-2 sm:mb-4 lg:mb-5">
          {data.pages.map(({ data }) =>
            data.map((notification) => (
              <NotificationItem
                notification={notification}
                className="first:border-t border-t-0 border odd:bg-skin-secondary px-4 py-2"
              />
            ))
          )}
          {!isLoading && !loadingMore ? (
            <Waypoint onEnter={loadMoreItems} />
          ) : (
            <div className="py-2 flex justify-center">
              <Spinner size="sm" color="primary" />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationsInfiniteList;

import { useOrderQuery } from '@framework/order/get-order';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-export-i18n';
import NoResult from '@components/common/no-result';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import OrderStatus from './order-status';
import PageLoader from '@components/ui/loaders/page-loader';
import { formatAddress } from '@utils/format-address';
import Button from '@components/ui/button';
import { useModalAction } from '@components/common/modal/modal.context';
import OrderItemCard from './order-item-card';
import Link from '@components/ui/link';
import { ROUTES } from '@utils/routes';
import { getIsTNG } from '@utils/use-tng';
import PriceRenderer from '@components/common/price-renderer';
import CopyToClipboard from 'react-copy-to-clipboard';
import CopyText from '@components/common/copy-text';
import ExternalLinkIcon from '@components/icons/external-link-icon';
import {
  getProductVariantImage,
  getProductVariantName,
} from '@framework/product/product.utils';
import { generateOrderLinkHref } from '@framework/utils/data-mappers';
import { OrderItem } from '@framework/types';
import { Capacitor } from '@capacitor/core';
import dynamic from 'next/dynamic';
import { formatNumber } from '@utils/format-number';
import OrderPayNowButton from './order-pay-now-button';

const OrderDownloadLink = dynamic(
  () => import('@components/order/order-download-link')
);

const OrderDetails: React.FC<{ className?: string }> = ({
  className = 'pt-10 lg:pt-12',
}) => {
  const { t } = useTranslation();
  const { openModal } = useModalAction();
  const {
    query: { order_id: id },
    pathname,
    asPath,
  } = useRouter();
  const {
    data: order,
    isLoading,
    error,
  } = useOrderQuery(id?.toString()!, pathname.indexOf('track-order') > -1);
  function handleCancelOrder(item: any) {
    return openModal('CANCEL_ORDER', item);
  }

  if (error) {
    return (
      <div>
        <div>
          <h4>Order Number: {id?.toString()!}</h4>
        </div>
        <NoResult />
      </div>
    );
  }

  if (isLoading || !order) return <PageLoader />;

  dayjs.extend(utc);
  dayjs.extend(timezone);

  const tracking_number_attribute =
    order && order.attributes
      ? order.attributes.find(
          (attribute: any) => attribute.slug == 'tracking-number'
        )
      : null;
  const tracking_number =
    tracking_number_attribute && tracking_number_attribute.pivot
      ? tracking_number_attribute.pivot.value
      : null;
  const can_cancel = order.can_cancel && pathname.indexOf('track-order') == -1;

  return (
    <div className={className}>
      <div className="mt-5 mb-2 lg:mt-10 lg:mb-4 flex justify-center items-center">
        <OrderStatus status={order.status?.serial || 0} />
      </div>
      <div className="mt-6 md:mt-10 mb-4 flex flex-col sm:flex-row sm:items-end">
        {tracking_number && (
          <div className="flex flex-col md:flex-row md:gap-2 items-start md:items-end mb-2 sm:mb-0">
            <span className="flex">
              Tracking Number
              {/* {!getIsTNG() && (
                <Link
                  href={`https://esvr5.gdexpress.com/WebsiteEtracking/Home/Etracking?id=GDEX&input=${tracking_number}`}
                  target="_blank"
                  className="text-skin-primary"
                >
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
              )} */}
            </span>
            <span className="flex">
              <span className="text-sm md:text-base">{tracking_number}</span>
              <CopyText text={tracking_number} />
            </span>
          </div>
        )}
        <div className="flex items-center justify-center divide-x ms-auto">
          {order.refund ? (
            <Link
              href={generateOrderLinkHref(
                id! as string,
                ROUTES.CANCEL_ORDER,
                pathname.indexOf('track-order') > -1 ? { track: 1 } : {}
              )}
              className="text-skin-primary text-center text-sm md:text-base px-2"
            >
              {t('text-view-cancellation')}
            </Link>
          ) : (
            <></>
          )}
          {!getIsTNG() && !Capacitor.isNativePlatform() ? (
            <div className="text-center text-sm md:text-base px-2">
              <OrderDownloadLink text="Print Order" order={order} />
            </div>
          ) : (
            <Link
              href={generateOrderLinkHref(
                id! as string,
                pathname.indexOf('track-order') > -1
                  ? ROUTES.TRACK_ORDER_PRINT
                  : ROUTES.ORDERS_PRINT
              )}
              className="px-2"
            >
              {t('text-print-order')}
            </Link>
          )}
        </div>
      </div>
      <ul className="border border-skin-base bg-skin-secondary rounded-md flex flex-col mt-6 xl:mt-7 md:flex-row mb-7 lg:mb-8 xl:mb-10">
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-skin-two px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-xs block text-skin-muted font-normal leading-5">
            {t('text-order-number')}:
          </span>
          {order?.display_id}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-date')}:
          </span>
          {dayjs
            .utc(order.created_at)
            .tz(dayjs.tz.guess())
            .format('MMM DD, YYYY')}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-email')}:
          </span>
          {order?.customer_email}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-total')}:
          </span>
          <PriceRenderer price={order.total} />
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-payment-method')}:
          </span>
          <span className="uppercase">
            {order?.payment_method
              ? order?.payment_method.title
              : order?.payment_gateway}
          </span>
        </li>
      </ul>

      <table className="w-full text-skin-base font-semibold text-sm lg:text-base">
        <thead>
          <tr>
            <th className="bg-skin-secondary p-4 text-start first:rounded-ts-md w-1/2">
              {t('text-product')}
            </th>
            <th className="bg-skin-secondary p-4 text-start last:rounded-te-md w-1/2">
              {t('text-total')}
            </th>
          </tr>
        </thead>
        <tbody>
          {order?.products.map((product: OrderItem, index: number) => (
            <OrderItemCard key={index} product={product} />
          ))}
        </tbody>
        <tfoot>
          <tr className="odd:bg-skin-secondary">
            <td className="p-4">{t('text-sub-total')}:</td>
            <td className="p-4">
              <PriceRenderer price={order.amount} />
            </td>
          </tr>
          {order.points_discount && order.points_discount > 0 && (
            <tr className="odd:bg-skin-secondary">
              <td className="p-4">{t('text-redeem-discount')}:</td>
              <td className="flex justify-end items-center gap-1 p-4">
                {order?.points_discount_refunded &&
                order.points_discount_refunded > 0 ? (
                  <span>
                    -
                    <PriceRenderer
                      price={
                        order.points_discount - order.points_discount_refunded
                      }
                    />
                    <span className="text-xs text-skin-muted">
                      {' '}
                      ({formatNumber(
                        order.points_used - order.points_refunded
                      )}{' '}
                      point
                      {parseInt(
                        (order.points_used - order.points_refunded) as any
                      ) > 1
                        ? 's'
                        : ''}
                      )
                    </span>
                  </span>
                ) : (
                  <></>
                )}
                <span
                  className={
                    order?.points_discount_refunded &&
                    order.points_discount_refunded > 0
                      ? 'text-xs line-through'
                      : ''
                  }
                >
                  -<PriceRenderer price={order.points_discount} />
                  {order.points_used > 0 && (
                    <span className="text-xs text-skin-muted">
                      {' '}
                      ({formatNumber(order.points_used)} point
                      {parseInt(order.points_used) > 1 ? 's' : ''})
                    </span>
                  )}
                </span>
              </td>
            </tr>
          )}
          {order.coupon_discount && order.coupon_discount > 0 && (
            <tr className="odd:bg-skin-secondary">
              <td className="p-4">Voucher Discount:</td>
              <td className="p-4">
                -<PriceRenderer price={order.coupon_discount} />
              </td>
            </tr>
          )}
          {order.discount && order.discount > 0 && (
            <tr className="odd:bg-skin-secondary">
              <td className="p-4">{t('text-discount')}:</td>
              <td className="p-4">
                -<PriceRenderer price={order.discount} />
              </td>
            </tr>
          )}
          <tr className="odd:bg-skin-secondary">
            <td className="p-4">{t('text-shipping')}:</td>
            <td className="p-4">
              <PriceRenderer price={order.delivery_fee} />
            </td>
          </tr>
          {order.store_credit && order.store_credit > 0 && (
            <tr className="odd:bg-skin-secondary">
              <td className="p-4">{t('text-store-credit')}:</td>
              <td className="p-4">
                -<PriceRenderer price={order.store_credit} />
              </td>
            </tr>
          )}
          {order.shipping_method ? (
            <tr className="odd:bg-skin-secondary">
              <td className="p-4">{t('text-shipping-method')}:</td>
              <td className="p-4">{order.shipping_method.name}</td>
            </tr>
          ) : (
            <></>
          )}
          <tr className="odd:bg-skin-secondary">
            <td className="p-4">{t('text-payment-method')}:</td>
            <td className="p-4 uppercase">
              {order?.payment_method
                ? order?.payment_method.title
                : order?.payment_gateway}
            </td>
          </tr>
          <tr className="odd:bg-skin-secondary">
            <td className="p-4">{t('text-total')}:</td>
            <td className="p-4">
              <PriceRenderer price={order.total} />
            </td>
          </tr>
          <tr className="odd:bg-skin-secondary">
            <td className="p-4">{`Order Notes`}:</td>
            <td className="p-4">{order.delivery_remarks}</td>
          </tr>
        </tfoot>
      </table>

      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mt-6">
        {/* <div className="w-full sm:w-1/2 sm:pe-8 mb-6">
          <h3 className="text-skin-base font-semibold mb-3 pb-2 border-b border-border-200">
            {t('text-billing-address')}
          </h3>

          <div className="text-sm text-body flex flex-col items-start space-y-1">
            {order.billing_address && (
              <>
                <span>{order.billing_address?.recipient}</span>
                <span>{formatAddress(order.billing_address)}</span>
              </>
            )}
            {order.billing_address.contact_number ? (
              <span>{order.billing_address.contact_number}</span>
            ) : (
              order.customer_contact && <span>{order.customer_contact}</span>
            )}
          </div>
        </div> */}

        <div className="w-full sm:w-1/2 sm:ps-8 mb-6">
          <h3 className="text-skin-base font-semibold mb-3 pb-2 border-b border-border-200">
            {t('text-shipping-address')}
          </h3>

          <div className="text-sm text-body flex flex-col items-start space-y-1">
            {order.shipping_address && (
              <>
                <span>{order.shipping_address?.recipient}</span>
                <span>{formatAddress(order.shipping_address)}</span>
              </>
            )}
            {order.shipping_address.contact_number ? (
              <span>{order.shipping_address.contact_number}</span>
            ) : (
              order.customer_contact && <span>{order.customer_contact}</span>
            )}
          </div>
        </div>
      </div>
      {can_cancel && (
        <div className="mt-2 md:mt-4 mb-4 text-center flex gap-4 justify-center">
          <Button
            variant="small"
            onClick={() => handleCancelOrder(order)}
            className="bg-red-500"
          >
            {t('button-cancel-order')}
          </Button>
          <OrderPayNowButton order={order} />
        </div>
      )}
    </div>
  );
};

export default OrderDetails;

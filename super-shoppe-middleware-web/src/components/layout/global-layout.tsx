import React from 'react';
import { useRouter } from 'next/router';
import { ROUTES } from '@utils/routes';
import BottomNavigation from './bottom-navigation';

interface GlobalLayoutProps {
  children: React.ReactNode;
}

const GlobalLayout: React.FC<GlobalLayoutProps> = ({ children }) => {
  const router = useRouter();

  // List of public routes that don't require authentication
  const publicRoutes = [ROUTES.LOGIN, ROUTES.SIGN_UP];
  const isPublicRoute = publicRoutes.includes(router.pathname);

  // For public routes, don't show header
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // For protected routes, show content with bottom navigation
  return (
    <div className="min-h-screen bg-gray-100">
      <main className="pb-24">{children}</main>
      <BottomNavigation />
    </div>
  );
};

export default GlobalLayout;

import QRCodeCountdown from '@components/common/qr-countdown';
import Heading from '@components/ui/heading';
import { useCustomer } from '@contexts/customer.context';
import { useSettings } from '@contexts/settings.context';
import { useProfileTokenQuery } from '@framework/customer/use-profile-token.query';
import React, { useEffect, useState } from 'react';
import { IoReload } from 'react-icons/io5';
import ReactQRCode from 'react-qr-code';
import ReactBarcode from 'react-barcode';
import { useWindowSize } from 'react-use';
import { App } from '@capacitor/app';
import { ScreenBrightness } from '@capacitor-community/screen-brightness';
import { Capacitor } from '@capacitor/core';
import { useRouter } from 'next/router';

const COUNTDOWN_TIME = 10 * 60 * 1000;
const BRIGHTNESS = 1.0;

const Barcode = ({ code }: { code: string }) => {
  return (
    <div className="w-full flex justify-center">
      <ReactBarcode
        value={code}
        displayValue
        textAlign="center"
        background=""
        lineColor="#000"
        width={1.5}
        height={48}
        fontSize={12}
      />
    </div>
  );
};

const QRCode = ({ code }: { code: string }) => {
  const { qr_code_color = '#e85f23' } = useSettings();
  const { width } = useWindowSize();
  const code_size = width < 350 ? width - 80 : 256;
  return (
    <QRLayout>
      <ReactQRCode value={code} size={code_size} fgColor={qr_code_color} />
    </QRLayout>
  );
};

const QRLayout: React.FC = ({ children }) => {
  const { width } = useWindowSize();
  const code_size = width < 350 ? width - 80 : 256;
  return (
    <div className="w-full flex justify-center mt-4">
      {children ?? (
        <div
          style={{ width: `${code_size}px`, height: `${code_size}px` }}
        ></div>
      )}
    </div>
  );
};

const GetQR: React.FC<{ hideQR: any }> = ({ hideQR }) => {
  const router = useRouter();
  const [count_time, setCountTime] = useState(COUNTDOWN_TIME);
  const [showReload, setShowReload] = useState(false);
  const [prevBrightness, setPrevBrightness] = useState(-1);
  const { data, isLoading, refetch } = useProfileTokenQuery();

  async function saveBrightness() {
    setPrevBrightness((await ScreenBrightness.getBrightness()).brightness);
    ScreenBrightness.setBrightness({ brightness: BRIGHTNESS });
  }

  function revertBrightness() {
    if (Capacitor.isNativePlatform()) {
      ScreenBrightness.setBrightness({
        brightness:
          Capacitor.getPlatform() === 'ios'
            ? prevBrightness == -1
              ? 0.6
              : prevBrightness
            : -1,
      });
    }
  }

  useEffect(() => {
    if (Capacitor.isNativePlatform()) {
      saveBrightness();
      App.addListener('appStateChange', async ({ isActive }) => {
        if (isActive) saveBrightness();
        else revertBrightness();
      });
    }
  }, []);

  useEffect(() => {
    const handleStart = (url: string) => {
      App.removeAllListeners();
      revertBrightness();
      router.events.off('routeChangeStart', handleStart);
    };

    router.events.on('routeChangeStart', handleStart);

    return () => {
      router.events.off('routeChangeStart', handleStart);
    };
  }, []); // Add empty dependency array to prevent infinite re-renders

  if (isLoading || !data) return <QRLayout />;
  if (!data.token) return <span>Failed to obtain QR</span>;

  function handleCountdownTick(value: any) {
    setCountTime(value.total);
    // ONLY show regenerate QR code button after 1 minute
    if (!showReload && value.total <= COUNTDOWN_TIME - 60 * 1000) {
      setShowReload(true);
    }
  }

  function handleCountdownCompleted() {
    revertBrightness();
    hideQR();
  }

  function handleReloadQRCode() {
    setShowReload(false);
    setCountTime(COUNTDOWN_TIME);
    refetch();
  }

  return (
    <div className="flex flex-col items-center gap-6">
      <div className="text-center">
        <div className="grid grid-cols-3 justify-between items-end">
          <div></div>
          <div>
            <span className="text-sm text-muted">Expires after</span>
            <QRCodeCountdown
              date={Date.now() + count_time}
              onTick={handleCountdownTick}
              onComplete={handleCountdownCompleted}
            />
          </div>
          {showReload ? (
            <span onClick={handleReloadQRCode} className="ps-2 pb-2">
              <IoReload />
            </span>
          ) : (
            <></>
          )}
        </div>
      </div>
      <div className="sm:rounded sm:bg-gradient-to-tr sm:from-yellow-300 sm:to-yellow-500 sm:p-2">
        <div className="sm:rounded sm:bg-white sm:p-4">
          <Barcode code={data.token} />
          <QRCode code={data.token} />
        </div>
      </div>
    </div>
  );
};

const ShowQRCode = ({}: any) => {
  const [show_qr, setShowQR] = useState(false);

  function handleShowQR() {
    setShowQR(true);
  }

  return show_qr ? (
    <div className="py-3 md:py-5 px-2 md:px-3">
      <div className="min-h-[384px] flex justify-center items-center">
        <GetQR hideQR={() => setShowQR(false)} />
      </div>
    </div>
  ) : (
    <div className="rounded-md shadow-card py-3 md:py-5 px-2 md:px-3 bg-gradient-to-tr from-yellow-300 to-yellow-500">
      <div
        className="min-h-[384px] flex justify-center items-center"
        onClick={handleShowQR}
      >
        <span className="font-semibold text-white text-sm drop-shadow">
          Tap to show your QR
        </span>
      </div>
    </div>
  );
};

const ProfileQRCode = () => {
  const { customer } = useCustomer();

  return customer ? (
    <div>
      <Heading className="text-skin-primary text-center mb-3">
        Your QR Code
      </Heading>
      <ShowQRCode />
    </div>
  ) : (
    <></>
  );
};

export default ProfileQRCode;

import { App } from '@capacitor/app';
import { Toast } from '@capacitor/toast';
import { useEffect, useRef } from 'react';

const ManagedDeviceState = () => {
  const isPriorExit = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    let backButtonListener: any;
    let appStateListener: any;

    const setupListeners = async () => {
      backButtonListener = await App.addListener(
        'backButton',
        ({ canGoBack }) => {
          if (canGoBack) {
            window.history.back();
          } else {
            if (isPriorExit.current) {
              App.removeAllListeners();
              App.exitApp();
            } else {
              Toast.show({
                text: 'Press again to exit',
                duration: 'short',
              });
              isPriorExit.current = true;

              // Clear any existing timeout
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }

              // Set new timeout with cleanup
              timeoutRef.current = setTimeout(() => {
                if (isPriorExit.current) {
                  isPriorExit.current = false;
                }
                timeoutRef.current = null;
              }, 2000);
            }
          }
        }
      );

      appStateListener = await App.addListener('appStateChange', ({}) => {
        if (isPriorExit.current) {
          isPriorExit.current = false;
        }
      });
    };

    setupListeners();

    // Cleanup function
    return () => {
      // Clear timeout if component unmounts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Remove listeners
      if (backButtonListener) {
        backButtonListener.remove();
      }
      if (appStateListener) {
        appStateListener.remove();
      }
    };
  }, []);

  return <></>;
};

export default ManagedDeviceState;
